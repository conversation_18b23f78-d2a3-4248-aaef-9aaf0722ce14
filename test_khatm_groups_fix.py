#!/usr/bin/env python
"""
Test script to verify the anonymous user fix for /khatm/groups/ endpoint
Tests with random user tokens and without token
"""
import os
import django
import requests
import random
import json
from typing import List, Optional

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from rest_framework.authtoken.models import Token
from apps.account.models import User

class KhatmGroupsAPITester:
    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.endpoint = f"{base_url}/khatm/groups/"
        self.headers = {
            'User-Agent': 'dart:io',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
    
    def get_random_user_tokens(self, count: int = 3) -> List[str]:
        """Get random user tokens from database"""
        try:
            # Get random users who have tokens
            users_with_tokens = User.objects.filter(auth_token__isnull=False)
            if not users_with_tokens.exists():
                print("⚠️  No users with tokens found, creating some...")
                return self.create_test_tokens(count)
            
            random_users = random.sample(list(users_with_tokens), min(count, users_with_tokens.count()))
            tokens = []
            
            for user in random_users:
                token = Token.objects.get(user=user)
                tokens.append(token.key)
                print(f"📝 Selected user: {user.username or user.email or f'User-{user.id}'} (Token: {token.key[:10]}...)")
            
            return tokens
        except Exception as e:
            print(f"❌ Error getting tokens: {e}")
            return []
    
    def create_test_tokens(self, count: int) -> List[str]:
        """Create test tokens if none exist"""
        tokens = []
        users = User.objects.all()[:count]
        
        for user in users:
            token, created = Token.objects.get_or_create(user=user)
            tokens.append(token.key)
            if created:
                print(f"✅ Created token for user: {user.username or user.email or f'User-{user.id}'}")
        
        return tokens
    
    def test_with_token(self, token: str, test_name: str = "Authenticated") -> dict:
        """Test endpoint with authentication token"""
        headers = self.headers.copy()
        headers['Authorization'] = f'Token {token}'
        
        print(f"\n🔐 Testing {test_name} request...")
        print(f"Token: {token[:10]}...")
        
        try:
            response = requests.get(self.endpoint, headers=headers, timeout=10)
            result = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'token': token[:10] + "...",
                'error': None,
                'data_count': 0
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    result['data_count'] = len(data) if isinstance(data, list) else 0
                    print(f"✅ Success! Status: {response.status_code}, Groups: {result['data_count']}")
                    
                    # Show sample group info
                    if isinstance(data, list) and len(data) > 0:
                        sample = data[0]
                        print(f"📊 Sample group: {sample.get('name', 'N/A')} (Type: {sample.get('group_type', 'N/A')})")
                except json.JSONDecodeError:
                    print(f"⚠️  Response not JSON: {response.text[:100]}")
            else:
                result['error'] = response.text
                print(f"❌ Failed! Status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
            
            return result
            
        except requests.exceptions.ConnectionError:
            error_msg = "Connection failed - Make sure Django server is running"
            print(f"❌ {error_msg}")
            return {'status_code': 0, 'success': False, 'error': error_msg, 'token': token[:10] + "..."}
        except Exception as e:
            error_msg = f"Unexpected error: {e}"
            print(f"❌ {error_msg}")
            return {'status_code': 0, 'success': False, 'error': error_msg, 'token': token[:10] + "..."}
    
    def test_without_token(self) -> dict:
        """Test endpoint without authentication (anonymous)"""
        print(f"\n🔓 Testing Anonymous request...")
        
        try:
            response = requests.get(self.endpoint, headers=self.headers, timeout=10)
            result = {
                'status_code': response.status_code,
                'success': response.status_code == 200,
                'token': 'None (Anonymous)',
                'error': None,
                'data_count': 0
            }
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    result['data_count'] = len(data) if isinstance(data, list) else 0
                    print(f"✅ Success! Status: {response.status_code}, Public Groups: {result['data_count']}")
                    
                    # Show sample group info
                    if isinstance(data, list) and len(data) > 0:
                        sample = data[0]
                        print(f"📊 Sample group: {sample.get('name', 'N/A')} (Type: {sample.get('group_type', 'N/A')})")
                except json.JSONDecodeError:
                    print(f"⚠️  Response not JSON: {response.text[:100]}")
            else:
                result['error'] = response.text
                print(f"❌ Failed! Status: {response.status_code}")
                print(f"Response: {response.text[:200]}")
            
            return result
            
        except requests.exceptions.ConnectionError:
            error_msg = "Connection failed - Make sure Django server is running"
            print(f"❌ {error_msg}")
            return {'status_code': 0, 'success': False, 'error': error_msg, 'token': 'None'}
        except Exception as e:
            error_msg = f"Unexpected error: {e}"
            print(f"❌ {error_msg}")
            return {'status_code': 0, 'success': False, 'error': error_msg, 'token': 'None'}
    
    def run_comprehensive_test(self):
        """Run comprehensive test with multiple scenarios"""
        print("🧪 Starting comprehensive test for /khatm/groups/ endpoint")
        print("=" * 60)
        
        results = []
        
        # Test 1: Anonymous user
        anonymous_result = self.test_without_token()
        results.append(anonymous_result)
        
        # Test 2: Random authenticated users
        tokens = self.get_random_user_tokens(3)
        for i, token in enumerate(tokens, 1):
            auth_result = self.test_with_token(token, f"User {i}")
            results.append(auth_result)
        
        # Summary
        print("\n" + "=" * 60)
        print("📋 TEST SUMMARY")
        print("=" * 60)
        
        successful_tests = sum(1 for r in results if r['success'])
        total_tests = len(results)
        
        print(f"✅ Successful tests: {successful_tests}/{total_tests}")
        
        for i, result in enumerate(results):
            status = "✅ PASS" if result['success'] else "❌ FAIL"
            test_type = "Anonymous" if result['token'] == 'None (Anonymous)' else f"Auth ({result['token']})"
            print(f"{status} | {test_type} | Status: {result['status_code']} | Groups: {result['data_count']}")
        
        if successful_tests == total_tests:
            print("\n🎉 All tests passed! The AnonymousUser fix is working correctly!")
        else:
            print(f"\n⚠️  {total_tests - successful_tests} test(s) failed. Check the errors above.")
        
        return results

def main():
    tester = KhatmGroupsAPITester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()

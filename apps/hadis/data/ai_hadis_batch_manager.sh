#!/bin/bash

# =============================================================================
# AI <PERSON><PERSON> Batch Manager Script
# =============================================================================
# This script manages batch translation operations for the ai_importer.py (Hadis)
# 
# Mode 1 (create): Gets active languages, creates batches, saves batch IDs to JSON
# Mode 2 (process): Reads batch IDs from JSON, processes each batch
# Mode 3 (status): Check status of batches
#
# Author: Generated for Habib Backend - Hadis Translation
# Date: $(date +%Y-%m-%d)
# =============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# =============================================================================
# GLOBAL VARIABLES AND CONFIGURATION
# =============================================================================

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPT_NAME="$(basename "$0")"
LOG_FILE="${SCRIPT_DIR}/hadis_batch_manager.log"
JSON_FILE="${SCRIPT_DIR}/hadis_batch_ids.json"
PYTHON_SCRIPT="${SCRIPT_DIR}/ai_importer.py"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Exit codes
EXIT_SUCCESS=0
EXIT_ERROR=1
EXIT_INVALID_ARGS=2
EXIT_FILE_NOT_FOUND=3
EXIT_DEPENDENCY_ERROR=4

# Language configurations
declare -A LANGUAGES=(
    ["es"]="Spanish"
    ["de"]="German"
    ["uz"]="Uzbek"
    ["pt"]="Portuguese"
    ["bn"]="Bengali"
    ["tr"]="Turkish"
ض    ["id"]="Indonesian"
    ["sw"]="Swahili"
    ["tg"]="Tajik"
    ["gu"]="Gujarati"
    ["ul"]="Urdu_Roman"
    ["az"]="Azerbaijani"
    ["ru"]="Russian"
    ["ur"]="Urdu"
    ["en"]="English"
)

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$LOG_FILE"
}

log_info() {
    log "INFO" "$@"
    echo -e "${BLUE}[INFO]${NC} $*"
}

log_success() {
    log "SUCCESS" "$@"
    echo -e "${GREEN}[SUCCESS]${NC} $*"
}

log_warning() {
    log "WARNING" "$@"
    echo -e "${YELLOW}[WARNING]${NC} $*"
}

log_error() {
    log "ERROR" "$@"
    echo -e "${RED}[ERROR]${NC} $*" >&2
}

# =============================================================================
# VALIDATION FUNCTIONS
# =============================================================================

validate_dependencies() {
    log_info "Validating dependencies..."
    
    # Check if Python script exists
    if [[ ! -f "$PYTHON_SCRIPT" ]]; then
        log_error "Python script not found: $PYTHON_SCRIPT"
        return $EXIT_FILE_NOT_FOUND
    fi
    
    # Check if Python is available
    if ! command -v python &> /dev/null; then
        log_error "Python is not installed or not in PATH"
        return $EXIT_DEPENDENCY_ERROR
    fi
    
    log_success "All dependencies validated successfully"
    return $EXIT_SUCCESS
}

# =============================================================================
# BATCH CREATION FUNCTIONS
# =============================================================================

create_batches() {
    local test_mode=${1:-false}
    
    log_info "Starting batch creation process..."
    
    # Validate dependencies first
    if ! validate_dependencies; then
        log_error "Dependency validation failed"
        return $EXIT_DEPENDENCY_ERROR
    fi
    
    # Initialize JSON structure
    local json_content='{"batches": [], "created_at": "'$(date -Iseconds)'", "total_languages": 0}'
    echo "$json_content" > "$JSON_FILE"
    
    local batch_count=0
    local successful_batches=0
    local failed_batches=0
    
    # Process languages
    for lang_code in "${!LANGUAGES[@]}"; do
        local lang_name="${LANGUAGES[$lang_code]}"
        
        # In test mode, only process first 2 languages
        if [[ "$test_mode" == "true" && $batch_count -ge 2 ]]; then
            log_warning "Test mode: Stopping after 2 languages"
            break
        fi
        
        batch_count=$((batch_count + 1))
        
        log_info "[$batch_count/${#LANGUAGES[@]}] Creating batch for $lang_name ($lang_code)..."
        
        # Execute Python script to send translation requests
        local output
        if output=$(python "$PYTHON_SCRIPT" --lang_code "$lang_code" --lang_name "$lang_name" --action send 2>&1); then
            # Extract batch ID from output
            local batch_id
            if batch_id=$(echo "$output" | grep -o "Batch ID: [^[:space:]]*" | cut -d' ' -f3); then
                # Update JSON file with batch information
                local temp_file=$(mktemp)
                jq --arg lang_code "$lang_code" \
                   --arg lang_name "$lang_name" \
                   --arg batch_id "$batch_id" \
                   --arg timestamp "$(date -Iseconds)" \
                   '.batches += [{
                       "language_code": $lang_code,
                       "language_name": $lang_name,
                       "batch_id": $batch_id,
                       "created_at": $timestamp,
                       "status": "created"
                   }] | .total_languages += 1' "$JSON_FILE" > "$temp_file" && mv "$temp_file" "$JSON_FILE"
                
                successful_batches=$((successful_batches + 1))
                log_success "Batch created successfully for $lang_name: $batch_id"
            else
                failed_batches=$((failed_batches + 1))
                log_error "Failed to extract batch ID for $lang_name"
                log_error "Output: $output"
            fi
        else
            failed_batches=$((failed_batches + 1))
            log_error "Failed to create batch for $lang_name ($lang_code)"
            log_error "Error output: $output"
        fi
        
        # Add delay between requests to avoid rate limiting
        sleep 2
    done
    
    # Final summary
    log_info "Batch creation completed!"
    log_info "Total languages processed: $batch_count"
    log_success "Successful batches: $successful_batches"
    if [[ $failed_batches -gt 0 ]]; then
        log_error "Failed batches: $failed_batches"
    fi
    
    log_info "Batch IDs saved to: $JSON_FILE"
    
    return $EXIT_SUCCESS
}

# =============================================================================
# BATCH PROCESSING FUNCTIONS
# =============================================================================

process_batches() {
    log_info "Starting batch processing..."
    
    # Check if JSON file exists
    if [[ ! -f "$JSON_FILE" ]]; then
        log_error "Batch IDs file not found: $JSON_FILE"
        log_error "Please run 'create' mode first"
        return $EXIT_FILE_NOT_FOUND
    fi
    
    # Validate dependencies
    if ! validate_dependencies; then
        log_error "Dependency validation failed"
        return $EXIT_DEPENDENCY_ERROR
    fi
    
    # Read batch information from JSON
    local total_batches
    total_batches=$(jq -r '.batches | length' "$JSON_FILE")
    
    if [[ $total_batches -eq 0 ]]; then
        log_warning "No batches found in JSON file"
        return $EXIT_SUCCESS
    fi
    
    log_info "Found $total_batches batches to process"
    
    local processed_count=0
    local successful_count=0
    local failed_count=0
    
    # Process each batch
    for ((i=0; i<total_batches; i++)); do
        local lang_code lang_name batch_id
        lang_code=$(jq -r ".batches[$i].language_code" "$JSON_FILE")
        lang_name=$(jq -r ".batches[$i].language_name" "$JSON_FILE")
        batch_id=$(jq -r ".batches[$i].batch_id" "$JSON_FILE")
        
        processed_count=$((processed_count + 1))
        
        log_info "[$processed_count/$total_batches] Processing batch for $lang_name ($lang_code): $batch_id"
        
        # Execute Python script to check and update translations
        local output
        if output=$(python "$PYTHON_SCRIPT" --lang_code "$lang_code" --lang_name "$lang_name" --action check --batch_id "$batch_id" 2>&1); then
            successful_count=$((successful_count + 1))
            log_success "Batch processed successfully for $lang_name"
            
            # Update status in JSON
            local temp_file=$(mktemp)
            jq --arg index "$i" \
               --arg timestamp "$(date -Iseconds)" \
               '.batches[($index | tonumber)].status = "processed" | 
                .batches[($index | tonumber)].processed_at = $timestamp' "$JSON_FILE" > "$temp_file" && mv "$temp_file" "$JSON_FILE"
        else
            failed_count=$((failed_count + 1))
            log_error "Failed to process batch for $lang_name ($lang_code): $batch_id"
            log_error "Error output: $output"
            
            # Update status in JSON
            local temp_file=$(mktemp)
            jq --arg index "$i" \
               --arg timestamp "$(date -Iseconds)" \
               '.batches[($index | tonumber)].status = "failed" | 
                .batches[($index | tonumber)].failed_at = $timestamp' "$JSON_FILE" > "$temp_file" && mv "$temp_file" "$JSON_FILE"
        fi
        
        # Add delay between requests
        sleep 3
    done
    
    # Final summary
    log_info "Batch processing completed!"
    log_info "Total batches processed: $processed_count"
    log_success "Successful: $successful_count"
    if [[ $failed_count -gt 0 ]]; then
        log_error "Failed: $failed_count"
    fi
    
    return $EXIT_SUCCESS
}

# =============================================================================
# STATUS CHECK FUNCTIONS
# =============================================================================

check_status() {
    log_info "Checking batch status..."
    
    # Check if JSON file exists
    if [[ ! -f "$JSON_FILE" ]]; then
        log_error "Batch IDs file not found: $JSON_FILE"
        return $EXIT_FILE_NOT_FOUND
    fi
    
    # Validate dependencies
    if ! validate_dependencies; then
        log_error "Dependency validation failed"
        return $EXIT_DEPENDENCY_ERROR
    fi
    
    # Read batch information from JSON
    local total_batches
    total_batches=$(jq -r '.batches | length' "$JSON_FILE")
    
    if [[ $total_batches -eq 0 ]]; then
        log_warning "No batches found in JSON file"
        return $EXIT_SUCCESS
    fi
    
    log_info "Checking status for $total_batches batches"
    
    # Check each batch status
    for ((i=0; i<total_batches; i++)); do
        local lang_code lang_name batch_id
        lang_code=$(jq -r ".batches[$i].language_code" "$JSON_FILE")
        lang_name=$(jq -r ".batches[$i].language_name" "$JSON_FILE")
        batch_id=$(jq -r ".batches[$i].batch_id" "$JSON_FILE")
        
        log_info "[$((i+1))/$total_batches] Checking status for $lang_name ($lang_code): $batch_id"
        
        # Execute Python script to check batch status
        python "$PYTHON_SCRIPT" --lang_code "$lang_code" --lang_name "$lang_name" --action status --batch_id "$batch_id"
        
        # Add small delay
        sleep 1
    done
    
    return $EXIT_SUCCESS
}

# =============================================================================
# MAIN FUNCTION AND ARGUMENT PARSING
# =============================================================================

show_usage() {
    cat << EOF
Usage: $SCRIPT_NAME <MODE>

DESCRIPTION:
    AI Hadis Batch Manager - Manages batch translation operations for Hadis content

MODES:
    create      Mode 1: Create batches for all specified languages
                        and save batch IDs to JSON file

    create-test Mode 1: Create batches for first 2 languages only (for testing)

    process     Mode 2: Process batches using previously saved batch IDs

    status      Mode 3: Check status of all batches

EXAMPLES:
    $SCRIPT_NAME create       # Create batches and save IDs
    $SCRIPT_NAME create-test  # Create test batches (first 2 languages only)
    $SCRIPT_NAME process      # Process saved batches
    $SCRIPT_NAME status       # Check batch status

FILES:
    Input:  $PYTHON_SCRIPT
    Output: $JSON_FILE
    Log:    $LOG_FILE

LANGUAGES:
    Spanish (es), German (de), Uzbek (uz), Portuguese (pt), Bengali (bn),
    Turkish (tr), Indonesian (id), Swahili (sw), Tajik (tg), Gujarati (gu),
    Hausa (ha), Urdu Roman (ul), Azerbaijani (az), Russian (ru), Urdu (ur), English (en)

EXIT CODES:
    0 - Success
    1 - General error
    2 - Invalid arguments
    3 - File not found
    4 - Dependency error

EOF
}

main() {
    # Check if at least one argument is provided
    if [[ $# -eq 0 ]]; then
        log_error "No arguments provided"
        show_usage
        exit $EXIT_INVALID_ARGS
    fi
    
    local mode="$1"
    
    # Initialize log file
    log_info "Starting AI Hadis Batch Manager - Mode: $mode"
    
    case "$mode" in
        "create")
            create_batches false
            ;;
        "create-test")
            create_batches true
            ;;
        "process")
            process_batches
            ;;
        "status")
            check_status
            ;;
        "help"|"-h"|"--help")
            show_usage
            exit $EXIT_SUCCESS
            ;;
        *)
            log_error "Invalid mode: $mode"
            show_usage
            exit $EXIT_INVALID_ARGS
            ;;
    esac
    
    local exit_code=$?
    
    if [[ $exit_code -eq $EXIT_SUCCESS ]]; then
        log_success "AI Hadis Batch Manager completed successfully"
    else
        log_error "AI Hadis Batch Manager failed with exit code: $exit_code"
    fi
    
    exit $exit_code
}

# Execute main function with all arguments
main "$@"

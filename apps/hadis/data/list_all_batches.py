#!/usr/bin/env python3
"""
List All Batches Script
========================
This script fetches and displays ALL batch information directly from Anthropic API
without needing any local JSON files or batch IDs.

Author: Generated for Habib Backend - Hadis Translation
Date: 2025-07-28
"""

import os
import sys
from datetime import datetime
from typing import Dict, List, Optional
import anthropic
from tabulate import tabulate

# Add project root to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))
sys.path.insert(0, project_root)

# Clear proxy environment variables to avoid conflicts
proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'all_proxy', 'ALL_PROXY']
for var in proxy_vars:
    if var in os.environ:
        del os.environ[var]

# تنظیم متغیر محیطی برای تنظیمات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')

import django
django.setup()
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()

# Initialize Anthropic client
client = anthropic.Anthropic(
    api_key="************************************************************************************************************",
)

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def print_colored(text: str, color: str) -> None:
    """Print colored text to terminal"""
    print(f"{color}{text}{Colors.NC}")

def format_datetime(dt_obj) -> str:
    """Format datetime object or string for display"""
    try:
        if dt_obj is None:
            return "N/A"

        # If it's already a datetime object
        if isinstance(dt_obj, datetime):
            return dt_obj.strftime('%Y-%m-%d %H:%M:%S UTC')

        # If it's a string, parse it
        if isinstance(dt_obj, str) and dt_obj:
            dt = datetime.fromisoformat(dt_obj.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S UTC')

        return "N/A"
    except:
        return str(dt_obj) if dt_obj else "N/A"

def format_date_only(dt_obj) -> str:
    """Format datetime object or string for display (date only)"""
    try:
        if dt_obj is None:
            return "N/A"

        # If it's already a datetime object
        if isinstance(dt_obj, datetime):
            return dt_obj.strftime('%Y-%m-%d')

        # If it's a string, parse it
        if isinstance(dt_obj, str) and dt_obj:
            dt = datetime.fromisoformat(dt_obj.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d')

        return "N/A"
    except:
        return str(dt_obj) if dt_obj else "N/A"

def get_status_color(status: str) -> str:
    """Get color for status display"""
    status_colors = {
        'in_progress': Colors.YELLOW,
        'canceling': Colors.PURPLE,
        'ended': Colors.GREEN,
        'failed': Colors.RED,
        'errored': Colors.RED,
        'canceled': Colors.PURPLE,
        'expired': Colors.RED
    }
    return status_colors.get(status.lower(), Colors.WHITE)

def fetch_all_batches() -> List[Dict]:
    """Fetch all batches from Anthropic API with pagination"""
    print_colored("🔄 Fetching all batches from Anthropic API...", Colors.YELLOW)
    
    all_batches = []
    after_id = None
    page = 1
    
    try:
        while True:
            print(f"   📄 Fetching page {page}...", end=" ")
            
            # Prepare parameters for pagination
            params = {"limit": 100}  # Maximum allowed per page
            if after_id:
                params["after_id"] = after_id
            
            # Fetch batches
            response = client.beta.messages.batches.list(**params)
            
            # Add batches to our list
            batch_count = len(response.data)
            all_batches.extend(response.data)
            
            print_colored(f"✅ ({batch_count} batches)", Colors.GREEN)
            
            # Check if there are more pages
            if not response.has_more:
                break
                
            after_id = response.last_id
            page += 1
            
    except Exception as e:
        print_colored(f"❌ Error fetching batches: {e}", Colors.RED)
        return []
    
    print_colored(f"📊 Total batches found: {len(all_batches)}", Colors.CYAN)
    return all_batches

def display_summary_table(batches: List[Dict]) -> None:
    """Display a summary table of all batches"""
    print_colored("\n" + "="*120, Colors.CYAN)
    print_colored("📊 ALL BATCHES SUMMARY TABLE", Colors.CYAN)
    print_colored("="*120, Colors.CYAN)
    
    if not batches:
        print_colored("❌ No batches found", Colors.RED)
        return
    
    table_data = []
    for i, batch in enumerate(batches, 1):
        # Calculate total requests and success rate
        request_counts = batch.request_counts
        total_requests = (
            request_counts.processing + 
            request_counts.succeeded + 
            request_counts.errored + 
            request_counts.canceled + 
            request_counts.expired
        )
        
        success_rate = "N/A"
        if total_requests > 0:
            success_rate = f"{(request_counts.succeeded / total_requests) * 100:.1f}%"
        
        # Determine batch purpose/language (if identifiable from results_url or other info)
        batch_purpose = "Unknown"
        if hasattr(batch, 'results_url') and batch.results_url:
            batch_purpose = "Translation Batch"
        
        table_data.append([
            i,
            batch.id[:25] + "...",
            batch.processing_status,
            f"{request_counts.succeeded}/{total_requests}",
            success_rate,
            format_date_only(batch.created_at),
            format_date_only(batch.ended_at) if batch.ended_at else "Processing",
            "Yes" if batch.results_url else "No"
        ])
    
    headers = ["#", "Batch ID", "Status", "Success/Total", "Success Rate", "Created", "Ended", "Results"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

def display_detailed_batch_info(batch: Dict, index: int) -> None:
    """Display detailed information for a single batch"""
    print_colored(f"\n{'='*80}", Colors.BLUE)
    print_colored(f"📋 BATCH #{index}: {batch.id}", Colors.WHITE)
    print_colored(f"{'='*80}", Colors.BLUE)
    
    # Basic Information
    print_colored("🔍 Basic Information:", Colors.CYAN)
    print(f"   Batch ID: {batch.id}")
    status_color = get_status_color(batch.processing_status)
    print(f"   Processing Status: {status_color}{batch.processing_status}{Colors.NC}")
    print(f"   Created At: {format_datetime(batch.created_at)}")
    print(f"   Expires At: {format_datetime(batch.expires_at)}")
    
    if batch.ended_at:
        print(f"   Ended At: {format_datetime(batch.ended_at)}")
    
    if batch.cancel_initiated_at:
        print(f"   Cancel Initiated At: {format_datetime(batch.cancel_initiated_at)}")
    
    if batch.archived_at:
        print(f"   Archived At: {format_datetime(batch.archived_at)}")
    
    # Request Statistics
    request_counts = batch.request_counts
    total_requests = (
        request_counts.processing + 
        request_counts.succeeded + 
        request_counts.errored + 
        request_counts.canceled + 
        request_counts.expired
    )
    
    print_colored("\n📊 Request Statistics:", Colors.CYAN)
    print(f"   Total Requests: {total_requests}")
    print(f"   ✅ Succeeded: {Colors.GREEN}{request_counts.succeeded}{Colors.NC}")
    print(f"   🔄 Processing: {Colors.YELLOW}{request_counts.processing}{Colors.NC}")
    print(f"   ❌ Errored: {Colors.RED}{request_counts.errored}{Colors.NC}")
    print(f"   🚫 Canceled: {Colors.PURPLE}{request_counts.canceled}{Colors.NC}")
    print(f"   ⏰ Expired: {Colors.RED}{request_counts.expired}{Colors.NC}")
    
    # Success Rate
    if total_requests > 0:
        success_rate = (request_counts.succeeded / total_requests) * 100
        rate_color = Colors.GREEN if success_rate > 80 else Colors.YELLOW if success_rate > 50 else Colors.RED
        print(f"   📈 Success Rate: {rate_color}{success_rate:.1f}%{Colors.NC}")
    
    # Results URL
    print_colored("\n🔗 Results:", Colors.CYAN)
    if batch.results_url:
        print(f"   Results URL: Available")
        print(f"   URL: {batch.results_url}")
    else:
        print(f"   Results URL: Not available yet")

def display_statistics(batches: List[Dict]) -> None:
    """Display overall statistics about all batches"""
    if not batches:
        return
    
    print_colored(f"\n{'='*80}", Colors.CYAN)
    print_colored("📈 OVERALL STATISTICS", Colors.CYAN)
    print_colored(f"{'='*80}", Colors.CYAN)
    
    # Status distribution
    status_counts = {}
    total_requests = 0
    total_succeeded = 0
    total_errored = 0
    total_expired = 0
    total_canceled = 0
    
    for batch in batches:
        status = batch.processing_status
        status_counts[status] = status_counts.get(status, 0) + 1
        
        counts = batch.request_counts
        total_requests += counts.processing + counts.succeeded + counts.errored + counts.canceled + counts.expired
        total_succeeded += counts.succeeded
        total_errored += counts.errored
        total_expired += counts.expired
        total_canceled += counts.canceled
    
    print(f"📊 Total Batches: {len(batches)}")
    print(f"📊 Total Requests Across All Batches: {total_requests}")
    
    if total_requests > 0:
        overall_success_rate = (total_succeeded / total_requests) * 100
        rate_color = Colors.GREEN if overall_success_rate > 80 else Colors.YELLOW if overall_success_rate > 50 else Colors.RED
        print(f"📈 Overall Success Rate: {rate_color}{overall_success_rate:.1f}%{Colors.NC}")
    
    print("\n📋 Status Distribution:")
    for status, count in status_counts.items():
        status_color = get_status_color(status)
        print(f"   {status_color}{status.title()}: {count}{Colors.NC}")
    
    print(f"\n📊 Request Results Summary:")
    print(f"   ✅ Total Succeeded: {Colors.GREEN}{total_succeeded}{Colors.NC}")
    print(f"   ❌ Total Errored: {Colors.RED}{total_errored}{Colors.NC}")
    print(f"   ⏰ Total Expired: {Colors.RED}{total_expired}{Colors.NC}")
    print(f"   🚫 Total Canceled: {Colors.PURPLE}{total_canceled}{Colors.NC}")

def main():
    """Main function"""
    print_colored("🚀 List All Batches - Direct from Anthropic API", Colors.WHITE)
    print_colored("=" * 60, Colors.WHITE)
    
    # Fetch all batches
    batches = fetch_all_batches()
    
    if not batches:
        print_colored("❌ No batches found or error occurred", Colors.RED)
        sys.exit(1)
    
    # Display summary table
    display_summary_table(batches)
    
    # Display overall statistics
    display_statistics(batches)
    
    # Ask user for detailed view
    print_colored(f"\n{'='*60}", Colors.WHITE)
    print("Options:")
    print("  1. Show detailed information for all batches")
    print("  2. Show detailed information for specific batch (enter batch number)")
    print("  3. Export batch IDs to JSON file")
    print("  4. Exit")
    
    try:
        choice = input("\nEnter your choice (1-4): ").strip()
        
        if choice == "1":
            # Show all detailed information
            for i, batch in enumerate(batches, 1):
                display_detailed_batch_info(batch, i)
        
        elif choice == "2":
            # Show specific batch
            batch_num = int(input(f"Enter batch number (1-{len(batches)}): "))
            if 1 <= batch_num <= len(batches):
                display_detailed_batch_info(batches[batch_num-1], batch_num)
            else:
                print_colored("❌ Invalid batch number", Colors.RED)
        
        elif choice == "3":
            # Export to JSON
            import json
            export_data = {
                "batches": [
                    {
                        "batch_id": batch.id,
                        "processing_status": batch.processing_status,
                        "created_at": batch.created_at.isoformat() if isinstance(batch.created_at, datetime) else str(batch.created_at),
                        "ended_at": batch.ended_at.isoformat() if batch.ended_at and isinstance(batch.ended_at, datetime) else str(batch.ended_at) if batch.ended_at else None,
                        "expires_at": batch.expires_at.isoformat() if isinstance(batch.expires_at, datetime) else str(batch.expires_at),
                        "request_counts": {
                            "processing": batch.request_counts.processing,
                            "succeeded": batch.request_counts.succeeded,
                            "errored": batch.request_counts.errored,
                            "canceled": batch.request_counts.canceled,
                            "expired": batch.request_counts.expired
                        },
                        "results_url": batch.results_url
                    }
                    for batch in batches
                ],
                "exported_at": datetime.now().isoformat(),
                "total_batches": len(batches)
            }
            
            filename = f"all_batches_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            print_colored(f"✅ Batch data exported to: {filename}", Colors.GREEN)
        
        elif choice == "4":
            print_colored("👋 Goodbye!", Colors.GREEN)
        
        else:
            print_colored("❌ Invalid choice", Colors.RED)
    
    except (ValueError, KeyboardInterrupt):
        print_colored("\n👋 Goodbye!", Colors.GREEN)

if __name__ == "__main__":
    main()

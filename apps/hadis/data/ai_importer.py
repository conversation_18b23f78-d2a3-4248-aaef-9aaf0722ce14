import os
import sys
import json
import time
import argparse
from django.db import models
from django.db.models import Q
from django.core.wsgi import get_wsgi_application
import requests
import anthropic

# Add project root to Python path for server environment
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))
sys.path.insert(0, project_root)

# تنظیم متغیر محیطی برای تنظیمات Django - سرور
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.production')

import django
django.setup()
application = get_wsgi_application()

# Import after Django setup
from utils.telegram_logger import telegram_logger

# مطمئن شوید که مدل‌های لازم را import کرده‌اید
from apps.hadis.models import Hadis

# تنظیم argparse برای دریافت مقادیر target_language_code و target_language_name از خط فرمان
parser = argparse.ArgumentParser(description='Batch Translation Script for Hadiths')
parser.add_argument('--lang_code', type=str, help='Target language code', required=True)
parser.add_argument('--lang_name', type=str, help='Target language name', required=True)
parser.add_argument('--action', type=str, help='Batch Translation Manager', required=True)
parser.add_argument('--batch_id', type=str, help='Batch Translation Manager')
args = parser.parse_args()

# متغیر زبان مقصد از ورودی خط فرمان
target_language_code = args.lang_code
target_language_name = args.lang_name
action = args.action
batch_id = args.batch_id

client = anthropic.Anthropic(
    api_key="************************************************************************************************************",
)

def prepare_translation_requests(text_items):
    """
    آماده‌سازی درخواست‌های ترجمه برای پردازش دسته‌ای
    """
    requests = []
    for idx, (text, item_id) in enumerate(text_items):
        if target_language_code == "ul":
            prompt = f"""Translate the following hadith from one of the Shia Imams into Roman Urdu.

This hadith carries deep religious and spiritual meanings rooted in Islamic and Shia culture. The translation should:

Be faithful to the original meaning, while respecting Shia theological and cultural context.

Use simple and clear vocabulary appropriate for Roman Urdu readers.

Be fluent and natural, not just a word-for-word rendering.

Include brief clarifying phrases if needed to convey the full message — but avoid long commentary.

Provide only the translation, with no extra text or explanation."""
        else:
            prompt = f"""
            Translate this hadith from one of the Shia Imams into {target_language_name}.
            Hadith contains deep religious meanings within the context of Islamic and Shia culture.
            The translation should be accurate and culturally appropriate, considering the Islamic and Shia context.
            The goal is for the translation to be fluent and easily understandable by native {target_language_name} speakers.
            Please provide ONLY the translation without any extra text.
            """
        prompt += f"\n{text}"

        requests.append({
            "custom_id": f"hadith__{item_id}",
            "params": {
                "model": "claude-3-7-sonnet-latest",
                "max_tokens": 2048,
                "messages": [{"role": "user", "content": prompt}]
            }
        })
    return requests

def send_translation_requests():
    """
    ارسال درخواست‌های ترجمه به صورت دسته‌ای
    """
    # استخراج تمامی حدیث‌هایی که دارای ترجمه به زبان عربی یا فارسی هستند
    hadis_list = Hadis.objects.filter(
        Q(translations__contains=[{'language_code': 'ar'}]) |
        Q(translations__contains=[{'language_code': 'fa'}])
    )

    # شمارش تعداد احادیثی که هنوز ترجمه نشده‌اند
    untranslated_hadis_list = [hadis for hadis in hadis_list if not any(tr['language_code'] == target_language_code for tr in hadis.translations)]
    total_untranslated_hadis = len(untranslated_hadis_list)

    telegram_logger(f"Starting the translation process for {total_untranslated_hadis} Hadiths without {target_language_name} translations...")

    text_items = []
    for hadis in untranslated_hadis_list:
        arabic_translation = hadis.text
        persian_translation = hadis.get_translation('fa')
        combined_text = f"Arabic: {arabic_translation}\n\nPersian: {persian_translation}"
        text_items.append((combined_text, hadis.id))

    requests = prepare_translation_requests(text_items)

    # ذخیره درخواست‌ها در فایل JSON برای پیگیری
    with open(f'hadith_translation_map_{target_language_code}.json', 'w', encoding='utf-8') as f:
        json.dump(requests, f, ensure_ascii=False, indent=4)

    # ارسال درخواست‌ها به صورت دسته‌ای
    batch = client.beta.messages.batches.create(requests=requests)
    telegram_logger(f"Batch translation requests sent. Batch detail: {batch}")
    print(f"Batch translation requests sent. Batch ID: {batch.id}")

def check_batch_status():
    """
    بررسی وضعیت batch
    """
    batch = client.beta.messages.batches.retrieve(batch_id)
    telegram_logger(f"Batch {batch_id} status: {batch.processing_status}")
    print(f"Batch {batch_id} status: {batch.processing_status}")

def check_and_update_translations():
    """
    بررسی نتایج ترجمه و به‌روزرسانی پایگاه داده
    """
    # ابتدا وضعیت batch را بررسی می‌کنیم
    batch = client.beta.messages.batches.retrieve(batch_id)

    if batch.processing_status == "in_progress":
        print(f"Batch {batch_id} is still processing. Please try again later.")
        print(f"Current status: {batch.processing_status}")
        print(f"Request counts: Processing: {batch.request_counts.processing}, "
              f"Succeeded: {batch.request_counts.succeeded}, "
              f"Errored: {batch.request_counts.errored}")
        return

    if batch.results_url is None:
        print(f"Batch {batch_id} has no results_url available. Status: {batch.processing_status}")
        return

    # اگر به اینجا برسیم، batch پردازش شده و results_url دارد
    results = client.beta.messages.batches.results(batch_id)
    successful_translations = 0
    processed_hadis_ids = []

    for result in results:
        custom_id = result.custom_id
        translation = result.result.message.content[0].text if result.result.type == "succeeded" else None

        if not translation:
            continue

        # استخراج شناسه حدیث از custom_id
        _, item_id = custom_id.split("__")
        item_id = int(item_id)

        # به‌روزرسانی حدیث با ترجمه جدید
        hadis = Hadis.objects.get(id=item_id)
        if not any(t.get("language_code") == target_language_code for t in hadis.translations):
            hadis.translations.append({
                'language_code': target_language_code,
                'text': translation,
                'is_ai': True
            })
            hadis.save()
            successful_translations += 1
            processed_hadis_ids.append(item_id)

            # فقط هر 50 ترجمه یک بار لاگ ارسال کنیم
            if successful_translations % 50 == 0:
                try:
                    telegram_logger(f"✅ {successful_translations} hadiths translated to {target_language_name} so far...")
                except Exception as e:
                    print(f"Telegram log failed: {e}")

    # ارسال لاگ نهایی
    try:
        final_message = (
            f"🎉 Batch {batch_id} completed!\n\n"
            f"📊 Language: {target_language_name} ({target_language_code})\n"
            f"✅ Successful translations: {successful_translations}\n"
            f"📝 Processed Hadis IDs: {len(processed_hadis_ids)}\n\n"
            f"First 10 IDs: {processed_hadis_ids[:10]}"
        )
        telegram_logger(final_message)
    except Exception as e:
        print(f"Final telegram log failed: {e}")

    print(f"Batch {batch_id} processed and database updated with {successful_translations} successful translations.")

def main():
    """
    تابع اصلی برای مدیریت عملیات‌های مختلف
    """
    if action == "send":
        send_translation_requests()
    elif action == "check":
        check_and_update_translations()
    elif action == "status":
        check_batch_status()
    else:
        print("Invalid action. Use 'send', 'check', or 'status'.")

if __name__ == "__main__":
    main()

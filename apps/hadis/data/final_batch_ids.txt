msgbatch_01Ax8pwvwYoc5eZKYpJfDPqf
msgbatch_018Pp7Aj13mFzeX7GnUY4zeJ
msgbatch_018Pp7Aj13mFzeX7GnUY4zeJ
msgbatch_018Pp7Aj13mFzeX7GnUY4zeJ
msgbatch_01LoJ15C8c5WhudSjhKrQ6kp
msgbatch_01LoJ15C8c5WhudSjhKrQ6kp
msgbatch_01LoJ15C8c5WhudSjhKrQ6kp
msgbatch_013Evo76634h49wfGoViXoyW
msgbatch_013Evo76634h49wfGoViXoyW
msgbatch_013Evo76634h49wfGoViXoyW
msgbatch_01F6hBb4TSAwSFzPVUpWXErx
msgbatch_01F6hBb4TSAwSFzPVUpWXErx
msgbatch_01F6hBb4TSAwSFzPVUpWXErx
msgbatch_01RCQa1YdkubfJzAbQH1nKZy
msgbatch_01RCQa1YdkubfJzAbQH1nKZy
msgbatch_01RCQa1YdkubfJzAbQH1nKZy
msgbatch_01WubrgtsVbCYLuZkvvP1utx
msgbatch_01WubrgtsVbCYLuZkvvP1utx
msgbatch_01WubrgtsVbCYLuZkvvP1utx
msgbatch_01WubrgtsVbCYLuZkvvP1utx


[
    {"batch_id": "msgbatch_01UsgTiVaLGvGrD8yWiUtzQt", "language": "Portuguese", "language_code": "pt"},
    {"batch_id": "msgbatch_01Ghz53JX9HQgxfQVZ6pMvPm", "language": "Russian", "language_code": "ru"},
    {"batch_id": "msgbatch_011B6KsMZqCCTR6UQbHWBAL3", "language": "Turkish", "language_code": "tr"},
    {"batch_id": "msgbatch_012bcCjTewv6ktEUFE8PVh9r", "language": "Tajik", "language_code": "tg"},
    {"batch_id": "msgbatch_01GQ2XM2MNr3rSEy7rzEWoXG", "language": "English", "language_code": "en"},
    {"batch_id": "msgbatch_016utwnF1c5v5nDMX1jjoRAD", "language": "Spanish", "language_code": "es"},
    {"batch_id": "msgbatch_013SnUYLL6XbyT32sZHchsYY", "language": "Gujarati", "language_code": "gu"},
    {"batch_id": "msgbatch_01NJ1ysaNvtQ7FpCtUbv5KUr", "language": "Bengali", "language_code": "bn"},
    {"batch_id": "msgbatch_01MkWqdK7rese1ydHexwudog", "language": "Urdu", "language_code": "ur"}
]
# 1. Portuguese (pt)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code pt --lang_name Portuguese --action check --batch_id msgbatch_01UsgTiVaLGvGrD8yWiUtzQt

# 2. Russian (ru)  
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code ru --lang_name Russian --action check --batch_id msgbatch_01Ghz53JX9HQgxfQVZ6pMvPm

# 3. Turkish (tr)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code tr --lang_name Turkish --action check --batch_id msgbatch_011B6KsMZqCCTR6UQbHWBAL3

# 4. Tajik (tg)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code tg --lang_name Tajik --action check --batch_id msgbatch_012bcCjTewv6ktEUFE8PVh9r

# 5. English (en)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code en --lang_name English --action check --batch_id msgbatch_01GQ2XM2MNr3rSEy7rzEWoXG

# 6. Spanish (es)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code es --lang_name Spanish --action check --batch_id msgbatch_016utwnF1c5v5nDMX1jjoRAD

# 7. Gujarati (gu)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code gu --lang_name Gujarati --action check --batch_id msgbatch_013SnUYLL6XbyT32sZHchsYY

# 8. Bengali (bn)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code bn --lang_name Bengali --action check --batch_id msgbatch_01NJ1ysaNvtQ7FpCtUbv5KUr

# 9. Urdu (ur)
cd /home/<USER>/web && python apps/hadis/data/ai_importer.py --lang_code ur --lang_name Urdu --action check --batch_id msgbatch_01MkWqdK7rese1ydHexwudog
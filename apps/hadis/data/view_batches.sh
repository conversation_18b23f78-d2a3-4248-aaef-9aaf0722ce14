#!/bin/bash

# =============================================================================
# Batch List Viewer Shell Script
# =============================================================================
# This script runs the batch_list_viewer.py to display all batch information
# 
# Author: Generated for Habib Backend - Hadis Translation
# Date: 2025-07-28
# =============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="${SCRIPT_DIR}/batch_list_viewer.py"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Batch List Viewer...${NC}"
echo -e "${BLUE}================================${NC}"

# Check if Python script exists
if [[ ! -f "$PYTHON_SCRIPT" ]]; then
    echo -e "${RED}❌ Error: Python script not found: $PYTHON_SCRIPT${NC}"
    exit 1
fi

# Check if Python is available
if ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Error: Python is not installed or not in PATH${NC}"
    exit 1
fi

# Install required packages if not available
echo -e "${YELLOW}📦 Checking required packages...${NC}"
python -c "import tabulate" 2>/dev/null || {
    echo -e "${YELLOW}📦 Installing tabulate package...${NC}"
    pip install tabulate
}

python -c "import anthropic" 2>/dev/null || {
    echo -e "${YELLOW}📦 Installing anthropic package...${NC}"
    pip install anthropic
}

echo -e "${GREEN}✅ All packages are available${NC}"
echo ""

# Run the Python script
python "$PYTHON_SCRIPT"

echo -e "\n${GREEN}✅ Batch List Viewer completed${NC}"

#!/usr/bin/env python3
"""
Batch List Viewer Script
=========================
This script displays detailed information about all batch API keys
from the hadis_batch_ids.json file with their current status and details.

Author: Generated for Habib Backend - Hadis Translation
Date: 2025-07-28
"""

import os
import json
import sys
from datetime import datetime
from typing import Dict, List, Optional
import anthropic
from tabulate import tabulate

# Add project root to Python path
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(script_dir)))
sys.path.insert(0, project_root)

# Clear proxy environment variables to avoid conflicts
proxy_vars = ['http_proxy', 'https_proxy', 'HTTP_PROXY', 'HTTPS_PROXY', 'all_proxy', 'ALL_PROXY']
for var in proxy_vars:
    if var in os.environ:
        del os.environ[var]

# تنظیم متغیر محیطی برای تنظیمات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')

import django
django.setup()
from django.core.wsgi import get_wsgi_application
application = get_wsgi_application()

# Initialize Anthropic client
client = anthropic.Anthropic(
    api_key="************************************************************************************************************",
)

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    PURPLE = '\033[0;35m'
    CYAN = '\033[0;36m'
    WHITE = '\033[1;37m'
    NC = '\033[0m'  # No Color

def print_colored(text: str, color: str) -> None:
    """Print colored text to terminal"""
    print(f"{color}{text}{Colors.NC}")

def load_batch_data(json_file: str) -> Optional[Dict]:
    """Load batch data from JSON file"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print_colored(f"❌ Error: JSON file not found: {json_file}", Colors.RED)
        return None
    except json.JSONDecodeError as e:
        print_colored(f"❌ Error: Invalid JSON format: {e}", Colors.RED)
        return None

def get_batch_details(batch_id: str) -> Optional[Dict]:
    """Get detailed information about a specific batch from Anthropic API"""
    try:
        batch = client.beta.messages.batches.retrieve(batch_id)
        return {
            'id': batch.id,
            'processing_status': batch.processing_status,
            'request_counts': {
                'processing': batch.request_counts.processing,
                'succeeded': batch.request_counts.succeeded,
                'errored': batch.request_counts.errored,
                'canceled': batch.request_counts.canceled,
                'expired': batch.request_counts.expired
            },
            'created_at': batch.created_at,
            'expires_at': batch.expires_at,
            'results_url': batch.results_url
        }
    except Exception as e:
        print_colored(f"⚠️  Warning: Could not retrieve details for batch {batch_id}: {e}", Colors.YELLOW)
        return None

def format_datetime(dt_str: str) -> str:
    """Format datetime string for display"""
    try:
        if dt_str:
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
            return dt.strftime('%Y-%m-%d %H:%M:%S UTC')
        return "N/A"
    except:
        return dt_str

def get_status_color(status: str) -> str:
    """Get color for status display"""
    status_colors = {
        'in_progress': Colors.YELLOW,
        'completed': Colors.GREEN,
        'succeeded': Colors.GREEN,
        'failed': Colors.RED,
        'errored': Colors.RED,
        'canceled': Colors.PURPLE,
        'expired': Colors.RED,
        'created': Colors.BLUE
    }
    return status_colors.get(status.lower(), Colors.WHITE)

def display_summary_table(batches_data: List[Dict]) -> None:
    """Display a summary table of all batches"""
    print_colored("\n" + "="*100, Colors.CYAN)
    print_colored("📊 BATCH SUMMARY TABLE", Colors.CYAN)
    print_colored("="*100, Colors.CYAN)
    
    table_data = []
    for i, batch in enumerate(batches_data, 1):
        batch_details = batch.get('api_details', {})
        
        # Calculate total requests
        request_counts = batch_details.get('request_counts', {})
        total_requests = sum([
            request_counts.get('processing', 0),
            request_counts.get('succeeded', 0),
            request_counts.get('errored', 0),
            request_counts.get('canceled', 0),
            request_counts.get('expired', 0)
        ])
        
        table_data.append([
            i,
            batch['language_name'],
            batch['language_code'],
            batch['batch_id'][:20] + "...",
            batch_details.get('processing_status', 'Unknown'),
            f"{request_counts.get('succeeded', 0)}/{total_requests}",
            format_datetime(batch.get('created_at', '')).split()[0]  # Just date
        ])
    
    headers = ["#", "Language", "Code", "Batch ID", "Status", "Success/Total", "Created"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))

def display_detailed_batch_info(batch: Dict, index: int) -> None:
    """Display detailed information for a single batch"""
    print_colored(f"\n{'='*80}", Colors.BLUE)
    print_colored(f"📋 BATCH #{index}: {batch['language_name']} ({batch['language_code']})", Colors.WHITE)
    print_colored(f"{'='*80}", Colors.BLUE)
    
    # Basic Information
    print_colored("🔍 Basic Information:", Colors.CYAN)
    print(f"   Language: {batch['language_name']} ({batch['language_code']})")
    print(f"   Batch ID: {batch['batch_id']}")
    print(f"   Local Status: {batch.get('status', 'Unknown')}")
    print(f"   Created At: {format_datetime(batch.get('created_at', ''))}")
    
    # API Details
    api_details = batch.get('api_details')
    if api_details:
        print_colored("\n🌐 API Status Details:", Colors.CYAN)
        status = api_details.get('processing_status', 'Unknown')
        status_color = get_status_color(status)
        print(f"   Processing Status: {status_color}{status}{Colors.NC}")
        print(f"   API Created At: {format_datetime(api_details.get('created_at', ''))}")
        print(f"   Expires At: {format_datetime(api_details.get('expires_at', ''))}")
        
        # Request Counts
        request_counts = api_details.get('request_counts', {})
        if request_counts:
            print_colored("\n📊 Request Statistics:", Colors.CYAN)
            total_requests = sum([
                request_counts.get('processing', 0),
                request_counts.get('succeeded', 0),
                request_counts.get('errored', 0),
                request_counts.get('canceled', 0),
                request_counts.get('expired', 0)
            ])
            
            print(f"   Total Requests: {total_requests}")
            print(f"   ✅ Succeeded: {Colors.GREEN}{request_counts.get('succeeded', 0)}{Colors.NC}")
            print(f"   🔄 Processing: {Colors.YELLOW}{request_counts.get('processing', 0)}{Colors.NC}")
            print(f"   ❌ Errored: {Colors.RED}{request_counts.get('errored', 0)}{Colors.NC}")
            print(f"   🚫 Canceled: {Colors.PURPLE}{request_counts.get('canceled', 0)}{Colors.NC}")
            print(f"   ⏰ Expired: {Colors.RED}{request_counts.get('expired', 0)}{Colors.NC}")
            
            # Success Rate
            if total_requests > 0:
                success_rate = (request_counts.get('succeeded', 0) / total_requests) * 100
                rate_color = Colors.GREEN if success_rate > 80 else Colors.YELLOW if success_rate > 50 else Colors.RED
                print(f"   📈 Success Rate: {rate_color}{success_rate:.1f}%{Colors.NC}")
        
        # Results URL
        results_url = api_details.get('results_url')
        if results_url:
            print_colored("\n🔗 Results:", Colors.CYAN)
            print(f"   Results URL: Available")
        else:
            print_colored("\n🔗 Results:", Colors.CYAN)
            print(f"   Results URL: Not available yet")
    else:
        print_colored(f"\n⚠️  Could not retrieve API details for this batch", Colors.YELLOW)

def main():
    """Main function"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    json_file = os.path.join(script_dir, 'hadis_batch_ids.json')
    
    print_colored("🚀 Batch List Viewer - Hadis Translation Batches", Colors.WHITE)
    print_colored("=" * 60, Colors.WHITE)
    
    # Load batch data
    batch_data = load_batch_data(json_file)
    if not batch_data:
        sys.exit(1)
    
    batches = batch_data.get('batches', [])
    if not batches:
        print_colored("❌ No batches found in the JSON file", Colors.RED)
        sys.exit(1)
    
    print_colored(f"📁 Found {len(batches)} batches in {json_file}", Colors.GREEN)
    print_colored(f"📅 Batches created at: {format_datetime(batch_data.get('created_at', ''))}", Colors.BLUE)
    
    # Fetch API details for each batch
    print_colored("\n🔄 Fetching batch details from Anthropic API...", Colors.YELLOW)
    
    for i, batch in enumerate(batches):
        print(f"   Fetching details for {batch['language_name']} ({i+1}/{len(batches)})...", end=" ")
        api_details = get_batch_details(batch['batch_id'])
        batch['api_details'] = api_details
        if api_details:
            print_colored("✅", Colors.GREEN)
        else:
            print_colored("❌", Colors.RED)
    
    # Display summary table
    display_summary_table(batches)
    
    # Ask user for detailed view
    print_colored(f"\n{'='*60}", Colors.WHITE)
    print("Options:")
    print("  1. Show detailed information for all batches")
    print("  2. Show detailed information for specific batch (enter batch number)")
    print("  3. Exit")
    
    try:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            # Show all detailed information
            for i, batch in enumerate(batches, 1):
                display_detailed_batch_info(batch, i)
        
        elif choice == "2":
            # Show specific batch
            batch_num = int(input(f"Enter batch number (1-{len(batches)}): "))
            if 1 <= batch_num <= len(batches):
                display_detailed_batch_info(batches[batch_num-1], batch_num)
            else:
                print_colored("❌ Invalid batch number", Colors.RED)
        
        elif choice == "3":
            print_colored("👋 Goodbye!", Colors.GREEN)
        
        else:
            print_colored("❌ Invalid choice", Colors.RED)
    
    except (ValueError, KeyboardInterrupt):
        print_colored("\n👋 Goodbye!", Colors.GREEN)

if __name__ == "__main__":
    main()

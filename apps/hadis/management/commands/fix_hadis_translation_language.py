#!/usr/bin/env python3
"""
Django Management Command: Fix Hadis Translation Language Code
==============================================================

This command finds Hadis ID 7079 and checks its translations.
If there are 2 translation objects and one has language code 'en',
it changes that language code to 'tg' (Tajik).

Usage:
    python manage.py fix_hadis_translation_language --test    # Test mode (no changes)
    python manage.py fix_hadis_translation_language --apply   # Apply changes

Author: Generated for Habib Backend - Hadis Translation Fix
Date: 2025-07-28
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from apps.hadis.models import Hadis
from dj_language.models import Language
import json


class Command(BaseCommand):
    help = 'Fix Hadis ID 7079 translation language code from EN to TG'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test',
            action='store_true',
            help='Run in test mode (no database changes)',
        )
        parser.add_argument(
            '--apply',
            action='store_true',
            help='Apply changes to database',
        )

    def handle(self, *args, **options):
        """Main command handler"""
        
        # Check arguments
        if not options['test'] and not options['apply']:
            raise CommandError('You must specify either --test or --apply')
        
        if options['test'] and options['apply']:
            raise CommandError('Cannot use both --test and --apply at the same time')
        
        test_mode = options['test']
        
        self.stdout.write(
            self.style.SUCCESS('🔍 Hadis Translation Language Code Fixer')
        )
        self.stdout.write('=' * 60)
        
        if test_mode:
            self.stdout.write(
                self.style.WARNING('⚠️  RUNNING IN TEST MODE - NO CHANGES WILL BE MADE')
            )
        else:
            self.stdout.write(
                self.style.ERROR('🚨 RUNNING IN APPLY MODE - CHANGES WILL BE MADE')
            )
        
        self.stdout.write('=' * 60)
        
        # Find Hadis ID 7079
        try:
            hadis = Hadis.objects.get(id=7079)
            self.stdout.write(f"✅ Found Hadis ID: {hadis.id}")
        except Hadis.DoesNotExist:
            raise CommandError('❌ Hadis with ID 7079 not found')
        
        # Display current hadis info
        self.stdout.write(f"📖 Hadis Text: {hadis.text[:100]}...")
        self.stdout.write(f"📊 Total translations: {len(hadis.translations)}")
        
        # Check translations
        if len(hadis.translations) != 2:
            self.stdout.write(
                self.style.WARNING(f"⚠️  Expected 2 translations, found {len(hadis.translations)}")
            )
            self.stdout.write("Current translations:")
            for i, trans in enumerate(hadis.translations, 1):
                self.stdout.write(f"  {i}. Language: {trans.get('language_code', 'N/A')}")
                self.stdout.write(f"     Text: {trans.get('text', 'N/A')[:50]}...")
                self.stdout.write(f"     Is AI: {trans.get('is_ai', 'N/A')}")
            
            if not test_mode:
                confirm = input("\nDo you want to continue anyway? (y/N): ")
                if confirm.lower() != 'y':
                    self.stdout.write(self.style.ERROR("❌ Operation cancelled"))
                    return
        
        # Find EN translation
        en_translation_index = None
        en_translation = None
        
        for i, trans in enumerate(hadis.translations):
            if trans.get('language_code') == 'en':
                en_translation_index = i
                en_translation = trans
                break
        
        if en_translation_index is None:
            raise CommandError("❌ No translation with language code 'en' found")
        
        self.stdout.write(f"✅ Found EN translation at index {en_translation_index}")
        
        # Display current EN translation
        self.stdout.write("\n📋 Current EN Translation:")
        self.stdout.write(f"   Language Code: {en_translation.get('language_code')}")
        self.stdout.write(f"   Text: {en_translation.get('text', 'N/A')[:100]}...")
        self.stdout.write(f"   Is AI: {en_translation.get('is_ai', 'N/A')}")
        
        # Check if TG language exists
        try:
            tg_language = Language.objects.get(code='tg')
            self.stdout.write(f"✅ Found TG language: {tg_language.name} ({tg_language.code})")
        except Language.DoesNotExist:
            raise CommandError("❌ Language with code 'tg' not found in Language model")
        
        # Show what will be changed
        self.stdout.write("\n🔄 Proposed Changes:")
        self.stdout.write(f"   Change language_code from: 'en' → 'tg'")
        self.stdout.write(f"   Translation text remains: {en_translation.get('text', 'N/A')[:50]}...")
        self.stdout.write(f"   Is AI remains: {en_translation.get('is_ai', 'N/A')}")
        
        if test_mode:
            self.stdout.write(
                self.style.SUCCESS("\n✅ TEST MODE: Changes would be applied successfully")
            )
            self.stdout.write("No actual database changes were made.")
            return
        
        # Apply changes
        self.stdout.write(f"\n🚀 Applying changes...")
        
        try:
            with transaction.atomic():
                # Create a copy of translations to modify
                updated_translations = hadis.translations.copy()
                
                # Update the EN translation to TG
                updated_translations[en_translation_index]['language_code'] = 'tg'
                
                # Save the updated translations
                hadis.translations = updated_translations
                hadis.save()
                
                self.stdout.write(
                    self.style.SUCCESS("✅ Successfully updated translation language code")
                )
                
                # Verify the change
                hadis.refresh_from_db()
                updated_translation = hadis.translations[en_translation_index]
                
                self.stdout.write("\n🔍 Verification:")
                self.stdout.write(f"   Updated language_code: {updated_translation.get('language_code')}")
                self.stdout.write(f"   Text: {updated_translation.get('text', 'N/A')[:50]}...")
                self.stdout.write(f"   Is AI: {updated_translation.get('is_ai', 'N/A')}")
                
                if updated_translation.get('language_code') == 'tg':
                    self.stdout.write(
                        self.style.SUCCESS("✅ Change verified successfully!")
                    )
                else:
                    raise CommandError("❌ Verification failed - language code not updated")
                
        except Exception as e:
            raise CommandError(f"❌ Error applying changes: {str(e)}")
        
        self.stdout.write("\n" + "=" * 60)
        self.stdout.write(
            self.style.SUCCESS("🎉 Command completed successfully!")
        )

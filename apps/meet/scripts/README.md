# اسکریپت دیباگ آواتار اپ میت

این اسکریپت برای دیباگ فرایند آپلود آواتار و ایجاد پروفایل ارائه‌دهنده در اپ میت طراحی شده است.

## ویژگی‌های اسکریپت

### 🎯 هدف اصلی
- تست فرایند آپلود آواتار از طریق `/upload-tmp-media/`
- تست ایجاد پروفایل ارائه‌دهنده از طریق `/habibmeet/meets/provider/profile/`
- شناسایی و ثبت خطاهای احتمالی در فرایند
- پاک‌سازی خودکار داده‌های تست

### 🔧 قابلیت‌ها
- **آپلود تصویر**: آپلود تصویر PNG با نام‌های فارسی تصادفی
- **ایجاد پروفایل**: ایجاد پروفایل با داده‌های تصادفی
- **پاک‌سازی خودکار**: حذف رکوردهای ایجاد شده پس از هر تست
- **ثبت نتایج**: ذخیره تمام نتایج در فایل JSON
- **تست مداوم**: امکان اجرای تست‌های متوالی با تاخیر قابل تنظیم

### 📊 داده‌های تست
- **نام‌های فارسی**: استفاده از نام‌های فارسی اسلاگی برای تصاویر
- **پروفایل‌های متنوع**: داده‌های نمونه برای انواع مختلف ارائه‌دهندگان
- **زبان‌های مختلف**: انتخاب تصادفی زبان‌ها از دیتابیس
- **شبکه‌های اجتماعی**: تولید لینک‌های تصادفی

## نحوه استفاده

### پیش‌نیازها
```bash
# نصب کتابخانه‌های مورد نیاز
pip install requests

# اطمینان از وجود Django و دسترسی به دیتابیس
```

### اجرای اسکریپت
```bash
cd /home/<USER>/Desktop/main/habbib/habib/backend
python apps/meet/scripts/avatar_debug_script.py
```

### گزینه‌های موجود
1. **اجرای یک تست**: تست یکبار آپلود و ایجاد پروفایل
2. **اجرای تست‌های مداوم**: تست‌های متوالی با تاخیر قابل تنظیم
3. **نمایش خلاصه نتایج**: آمار کلی از تست‌های انجام شده
4. **خروج**: پایان اسکریپت

## تنظیمات

### اطلاعات اتصال
```python
base_url = "https://habibapp.com"
token = "45361937ef716c7613c6ff4b13ff2ddc784bd66e"
user_agent = "dart:io"
```

### مسیرهای فایل
```python
original_image_path = "/home/<USER>/Desktop/main/habbib/habib/backend/apps/meet/scripts/habib_logo.png"
temp_image_dir = "/tmp/avatar_debug"
results_file = "avatar_debug_results.json"
```

## ساختار نتایج

### فایل JSON خروجی
```json
[
  {
    "timestamp": "2024-01-01T12:00:00",
    "image_name": "علی-رضا-1234.png",
    "user_id": 123,
    "upload_result": {
      "success": true,
      "path": "/tmp/xyz-علی-رضا-1234.png",
      "url": "https://habibapp.com/static/tmp/xyz-علی-رضا-1234.png",
      "response": {...}
    },
    "profile_data": {
      "full_name": "دکتر علی رضایی",
      "bio": "متخصص علوم قرآنی و تفسیر",
      "email": "<EMAIL>",
      "languages": ["fa", "ar"]
    },
    "profile_result": {
      "success": true,
      "response": {...},
      "status_code": 201
    },
    "cleanup_result": {
      "success": true,
      "profiles_deleted": 1,
      "requests_deleted": 1
    }
  }
]
```

## فرایند تست

### مراحل هر تست
1. **تولید نام تصادفی**: انتخاب نام فارسی + شماره تصادفی
2. **ایجاد کپی تصویر**: کپی تصویر اصلی با نام جدید
3. **آپلود تصویر**: ارسال به `/upload-tmp-media/`
4. **تولید داده‌های پروفایل**: ایجاد داده‌های تصادفی
5. **ایجاد پروفایل**: ارسال به `/habibmeet/meets/provider/profile/`
6. **پاک‌سازی**: حذف رکوردهای ایجاد شده
7. **ثبت نتیجه**: ذخیره در فایل JSON

### خطاهای قابل شناسایی
- خطاهای آپلود تصویر
- خطاهای validation در پروفایل
- مشکلات اتصال به سرور
- خطاهای دیتابیس
- مشکلات پاک‌سازی

## نکات مهم

### امنیت
- توکن احراز هویت در کد قرار دارد
- فقط برای محیط تست استفاده شود
- داده‌های تست پس از هر اجرا پاک می‌شوند

### عملکرد
- تصاویر موقت در `/tmp/avatar_debug` ذخیره می‌شوند
- فایل‌های موقت پس از هر تست پاک می‌شوند
- نتایج در فایل JSON محلی ذخیره می‌شوند

### محدودیت‌ها
- وابسته به دسترسی اینترنت
- نیاز به توکن معتبر
- وابسته به وجود تصویر نمونه

## عیب‌یابی

### خطاهای رایج
1. **خطای اتصال**: بررسی اتصال اینترنت و دسترسی به سرور
2. **خطای احراز هویت**: بررسی اعتبار توکن
3. **خطای فایل**: بررسی وجود تصویر نمونه
4. **خطای Django**: بررسی تنظیمات Django و دیتابیس

### لاگ‌ها
- تمام خطاها در کنسول نمایش داده می‌شوند
- جزئیات کامل در فایل JSON ذخیره می‌شوند
- آمار کلی در پایان نمایش داده می‌شود

## مثال اجرا

```bash
$ python apps/meet/scripts/avatar_debug_script.py

🎯 اسکریپت دیباگ آواتار اپ میت
==================================================

گزینه‌های موجود:
1. اجرای یک تست
2. اجرای تست‌های مداوم
3. نمایش خلاصه نتایج
4. خروج

انتخاب کنید (1-4): 2
تعداد تست‌ها (Enter برای بی‌نهایت): 5
تاخیر بین تست‌ها (ثانیه، پیش‌فرض 5): 3

🔄 شروع تست‌های مداوم...
⏱️ تاخیر بین تست‌ها: 3 ثانیه

==================================================
🔢 تست شماره 1
==================================================
🚀 شروع تست جدید...
📸 نام تصویر: علی-رضا-1234.png
⬆️ آپلود تصویر...
✅ تصویر با موفقیت آپلود شد: /tmp/xyz-علی-رضا-1234.png
👤 داده‌های پروفایل: دکتر علی رضایی
📝 ایجاد پروفایل...
🧹 پاک‌سازی رکوردها...
✅ پاک‌سازی انجام شد: {'success': True, 'profiles_deleted': 1, 'requests_deleted': 1}
✅ تست با موفقیت انجام شد
⏳ انتظار 3 ثانیه...
```

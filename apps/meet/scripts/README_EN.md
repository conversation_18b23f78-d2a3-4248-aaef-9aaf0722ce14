# Meet App Avatar Debug Script

This script is designed to debug the avatar upload process and provider profile creation in the Meet app.

## Features

### 🎯 Main Purpose
- Test avatar upload process via `/upload-tmp-media/`
- Test provider profile creation via `/habibmeet/meets/provider/profile/`
- Identify and log potential errors in the process
- Automatic cleanup of test data

### 🔧 Capabilities
- **Image Upload**: Upload PNG images with random Persian names
- **Profile Creation**: Create profiles with random data
- **Automatic Cleanup**: Delete created records after each test
- **Result Logging**: Save all results to JSON file
- **Continuous Testing**: Run sequential tests with configurable delay
- **File Renaming**: Efficient file handling using `os.rename()`

### 📊 Test Data
- **Persian Names**: Use Persian Islamic names for images
- **Diverse Profiles**: Sample data for different types of providers
- **Multiple Languages**: Random selection of languages from database
- **Social Media**: Generate random social media links

## Usage

### Prerequisites
```bash
# Install required libraries
pip install requests

# Ensure Django access and database connectivity
```

### Running the Script
```bash
cd /home/<USER>/Desktop/main/habbib/habib/backend
python apps/meet/scripts/avatar_debug_script.py
```

### Available Options
1. **Run single test**: One-time upload and profile creation test
2. **Run continuous tests**: Sequential tests with configurable delay
3. **Show results summary**: Overall statistics of performed tests
4. **Exit**: End the script

## Configuration

### Connection Information
```python
base_url = "https://habibapp.com"
token = "45361937ef716c7613c6ff4b13ff2ddc784bd66e"
user_agent = "dart:io"
```

### File Paths
```python
original_image_path = "/home/<USER>/Desktop/main/habbib/habib/backend/apps/meet/scripts/habib_logo.png"
temp_image_dir = "/tmp/avatar_debug"
results_file = "avatar_debug_results.json"
```

## Results Structure

### JSON Output File
```json
[
  {
    "timestamp": "2024-01-01T12:00:00",
    "image_name": "ali-reza-1234.png",
    "user_id": 123,
    "upload_result": {
      "success": true,
      "path": "/tmp/xyz-ali-reza-1234.png",
      "url": "https://habibapp.com/static/tmp/xyz-ali-reza-1234.png",
      "response": {...}
    },
    "profile_data": {
      "full_name": "Dr. Ali Rezaei",
      "bio": "Quran and Tafsir Specialist",
      "email": "<EMAIL>",
      "languages": ["fa", "ar"]
    },
    "profile_result": {
      "success": true,
      "response": {...},
      "status_code": 201
    },
    "cleanup_result": {
      "success": true,
      "profiles_deleted": 1,
      "requests_deleted": 1
    }
  }
]
```

## Test Process

### Steps for Each Test
1. **Generate Random Name**: Select Persian name + random number
2. **Create Image Copy**: Copy original image with new name
3. **Upload Image**: Send to `/upload-tmp-media/`
4. **Generate Profile Data**: Create random profile data
5. **Create Profile**: Send to `/habibmeet/meets/provider/profile/`
6. **Cleanup**: Delete created records
7. **Log Result**: Save to JSON file

### Detectable Errors
- Image upload errors
- Profile validation errors
- Server connection issues
- Database errors
- Cleanup problems

## Important Notes

### Security
- Authentication token is embedded in code
- Should only be used in test environment
- Test data is cleaned up after each run

### Performance
- Temporary images stored in `/tmp/avatar_debug`
- Temporary files cleaned up after each test
- Results saved to local JSON file

### Limitations
- Requires internet connectivity
- Needs valid authentication token
- Depends on sample image availability

## Troubleshooting

### Common Errors
1. **Connection Error**: Check internet connection and server access
2. **Authentication Error**: Verify token validity
3. **File Error**: Check sample image existence
4. **Django Error**: Check Django settings and database

### Logs
- All errors displayed in console
- Complete details saved to JSON file
- Overall statistics shown at the end
- All logs and messages are in English for better debugging

## Example Run

```bash
$ python apps/meet/scripts/avatar_debug_script.py

🎯 Meet App Avatar Debug Script
==================================================

Available options:
1. Run single test
2. Run continuous tests
3. Show results summary
4. Exit

Choose option (1-4): 2
Number of tests (press Enter for infinite): 5
Delay between tests in seconds (default: 5): 3

🔄 Starting continuous tests...
⏱️ Delay between tests: 3 seconds

==================================================
🔢 Test number 1
==================================================
🚀 Starting new test...
📸 Image name: ali-reza-1234.png
⬆️ Uploading image...
✅ Image uploaded successfully: /tmp/xyz-ali-reza-1234.png
👤 Profile data: Dr. Ali Rezaei
📝 Creating profile...
🧹 Cleaning up records...
✅ Cleanup completed: {'success': True, 'profiles_deleted': 1, 'requests_deleted': 1}
✅ Test completed successfully
⏳ Waiting 3 seconds...
```

## Technical Details

### File Handling
- Uses `os.rename()` for efficient file renaming
- Temporary files managed in `/tmp/avatar_debug/`
- Automatic cleanup prevents disk space issues

### API Integration
- Uses `requests.Session()` for persistent connections
- Proper error handling for HTTP responses
- Extracts relative paths from full URLs for FileFieldSerializer

### Error Recovery
- Comprehensive try-catch blocks
- Detailed error logging
- Graceful handling of cleanup failures

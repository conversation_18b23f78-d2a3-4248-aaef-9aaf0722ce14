"""
Django management command to trigger translation for incomplete OccasionDetail objects.

This command:
1. Finds all CalendarOccasions that have at least one OccasionDetail
2. Checks if each occasion has complete translations for all active languages
3. For incomplete occasions, triggers translation by re-saving the first OccasionDetail
"""

from django.core.management.base import BaseCommand, CommandError
from django.db.models import Count, Q
from django.db import transaction

from apps.najm_calendar.models import CalendarOccasions, OccasionDetail
from dj_language.models import Language


class Command(BaseCommand):
    help = 'Trigger translation for CalendarOccasions with incomplete OccasionDetail translations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually triggering translations',
        )
        parser.add_argument(
            '--occasion-id',
            type=int,
            help='Process only a specific occasion ID',
        )
        parser.add_argument(
            '--limit',
            type=int,
            default=None,
            help='Limit the number of occasions to process',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        occasion_id = options['occasion_id']
        limit = options['limit']

        self.stdout.write(
            self.style.SUCCESS('🚀 Starting OccasionDetail translation trigger process')
        )

        # Get all active languages
        active_languages = Language.objects.filter(status=True)
        active_language_codes = list(active_languages.values_list('code', flat=True))
        
        self.stdout.write(f"📋 Active languages: {', '.join(active_language_codes)}")
        self.stdout.write(f"📊 Total active languages: {len(active_language_codes)}")

        # Build queryset for occasions with at least one OccasionDetail
        occasions_queryset = CalendarOccasions.objects.annotate(
            detail_count=Count('occasion_details')
        ).filter(detail_count__gt=0).order_by('id')

        if occasion_id:
            occasions_queryset = occasions_queryset.filter(id=occasion_id)
            self.stdout.write(f"🎯 Processing specific occasion ID: {occasion_id}")

        if limit:
            occasions_queryset = occasions_queryset[:limit]
            self.stdout.write(f"📏 Limited to {limit} occasions")

        occasions = occasions_queryset
        total_occasions = occasions.count()

        if total_occasions == 0:
            self.stdout.write(
                self.style.WARNING('⚠️  No occasions found with OccasionDetail objects')
            )
            return

        self.stdout.write(f"📈 Found {total_occasions} occasions with OccasionDetail objects")
        self.stdout.write("=" * 60)

        processed_count = 0
        incomplete_count = 0
        triggered_count = 0

        for occasion in occasions:
            processed_count += 1
            
            self.stdout.write(f"\n🔍 [{processed_count}/{total_occasions}] Processing occasion: {occasion.id}")
            self.stdout.write(f"   Title: {occasion.title}")

            # Get all OccasionDetail objects for this occasion
            occasion_details = OccasionDetail.objects.filter(occasion=occasion)
            existing_language_codes = set(
                occasion_details.values_list('language__code', flat=True)
            )

            self.stdout.write(f"   📝 Existing details: {len(occasion_details)}")
            self.stdout.write(f"   🌐 Languages: {', '.join(sorted(existing_language_codes))}")

            # Check if all active languages are covered
            missing_languages = set(active_language_codes) - existing_language_codes
            
            if missing_languages:
                incomplete_count += 1
                self.stdout.write(
                    self.style.WARNING(f"   ❌ Missing languages: {', '.join(sorted(missing_languages))}")
                )

                # Find the first OccasionDetail to trigger translation
                # Prefer source languages (fa, en, ar) first
                source_detail = None
                for preferred_lang in ['fa', 'en', 'ar']:
                    source_detail = occasion_details.filter(language__code=preferred_lang).first()
                    if source_detail:
                        break
                
                # If no source language found, use the first available
                if not source_detail:
                    source_detail = occasion_details.first()

                if source_detail:
                    self.stdout.write(
                        f"   🎯 Source detail: ID {source_detail.id} (Language: {source_detail.language.code})"
                    )
                    self.stdout.write(f"   📄 Content preview: {source_detail.content[:100]}...")

                    if not dry_run:
                        try:
                            # Trigger translation by re-saving the source detail
                            # This will activate the post_save signal
                            with transaction.atomic():
                                source_detail.save()
                            
                            triggered_count += 1
                            self.stdout.write(
                                self.style.SUCCESS(f"   ✅ Translation triggered for occasion {occasion.id}")
                            )
                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(f"   ❌ Error triggering translation: {e}")
                            )
                    else:
                        self.stdout.write(
                            self.style.WARNING(f"   🧪 DRY RUN: Would trigger translation for occasion {occasion.id}")
                        )
                        triggered_count += 1
                else:
                    self.stdout.write(
                        self.style.ERROR(f"   ❌ No source detail found for occasion {occasion.id}")
                    )
            else:
                self.stdout.write(
                    self.style.SUCCESS(f"   ✅ Complete: All {len(active_language_codes)} languages covered")
                )

        # Summary
        self.stdout.write("\n" + "=" * 60)
        self.stdout.write(self.style.SUCCESS('📊 SUMMARY'))
        self.stdout.write(f"📈 Total occasions processed: {processed_count}")
        self.stdout.write(f"❌ Incomplete occasions: {incomplete_count}")
        self.stdout.write(f"✅ Complete occasions: {processed_count - incomplete_count}")
        
        if dry_run:
            self.stdout.write(f"🧪 Would trigger translations: {triggered_count}")
        else:
            self.stdout.write(f"🚀 Translations triggered: {triggered_count}")

        if incomplete_count > 0:
            completion_rate = ((processed_count - incomplete_count) / processed_count) * 100
            self.stdout.write(f"📊 Completion rate: {completion_rate:.1f}%")

        self.stdout.write(
            self.style.SUCCESS('✅ OccasionDetail translation trigger process completed!')
        )

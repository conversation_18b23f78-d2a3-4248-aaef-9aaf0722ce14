#!/usr/bin/env python3
"""
اسکریپت مستقل برای ترجمه کلمه به کلمه قرآن کریم
بدون وابستگی به Django
"""

import os
import sys
import json
import requests
import time
import argparse
from datetime import datetime
from typing import Dict, List, Tuple, Optional


class StandaloneTranslationConfig:
    """تنظیمات ترجمه مستقل"""
    
    def __init__(self):
        self.GEMINI_MODEL = "gemini-2.0-flash-lite"
        self.BATCH_SIZE = 10
        self.MAX_RETRIES = 3
        self.REQUEST_TIMEOUT = 30
        self.BATCH_DELAY = 2
        self.TEST_MODE = False
        self.TEST_WORD_LIMIT = 20
        
        self.DEFAULT_API_KEYS = [
            "AIzaSyBz8OtW_zJPRiDA4YxTOBxaw2sv3oU6gwY",
            "AIzaSyBJv9CfDRbCJu4uvFXUHEKNSSTFO47IebA",
            "AIzaSyDXdorZ9JrLYkhUJhb-LwXD9e_S-nVD_0w",
            "AIzaSyD7zMokI5CYuN3SDAGxo7s5zQbbnD6qIL8",
            "AIzaSyBZ7t5NhJl2XiCv6EuuzHxTJmtJyCZvfqA"
        ]
        
        # مسیرهای فایل
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.data_dir = os.path.join(self.base_dir, 'data')
        if self.TEST_MODE:
            self.data_dir = os.path.join(self.data_dir, 'test')
        
        os.makedirs(self.data_dir, exist_ok=True)
    
    def get_api_url(self, api_key: str) -> str:
        """تولید URL API"""
        return f"https://generativelanguage.googleapis.com/v1beta/models/{self.GEMINI_MODEL}:generateContent?key={api_key}"
    
    def get_output_file_path(self) -> str:
        """مسیر فایل خروجی"""
        filename = "main_az_test.json" if self.TEST_MODE else "main_az.json"
        return os.path.join(self.data_dir, filename)
    
    def get_progress_file_path(self) -> str:
        """مسیر فایل پیشرفت"""
        filename = "translation_progress_test.json" if self.TEST_MODE else "translation_progress.json"
        return os.path.join(self.data_dir, filename)
    
    def get_requests_log_path(self) -> str:
        """مسیر فایل لاگ درخواست‌ها"""
        filename = "translation_requests_test.json" if self.TEST_MODE else "translation_requests.json"
        return os.path.join(self.data_dir, filename)


class StandaloneGeminiClient:
    """کلاینت مستقل Gemini API"""
    
    def __init__(self, config: StandaloneTranslationConfig):
        self.config = config
        self.api_keys = config.DEFAULT_API_KEYS.copy()
        self.current_key_index = 0
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "api_key_rotations": 0
        }
    
    def test_api_key(self, api_key: str) -> bool:
        """تست کلید API"""
        try:
            url = self.config.get_api_url(api_key)
            payload = {
                "contents": [{
                    "parts": [{"text": "سلام"}]
                }]
            }
            
            response = requests.post(
                url,
                json=payload,
                timeout=self.config.REQUEST_TIMEOUT
            )
            
            return response.status_code == 200
            
        except Exception:
            return False
    
    def rotate_api_key(self) -> bool:
        """چرخش کلید API"""
        if len(self.api_keys) <= 1:
            return False
        
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
        self.stats["api_key_rotations"] += 1
        
        print(f"🔄 تغییر به کلید API شماره {self.current_key_index + 1}")
        return True
    
    def make_api_request(self, prompt: str) -> Tuple[Optional[str], bool, str]:
        """ارسال درخواست به API"""
        self.stats["total_requests"] += 1
        
        for attempt in range(self.config.MAX_RETRIES):
            try:
                current_key = self.api_keys[self.current_key_index]
                url = self.config.get_api_url(current_key)
                
                payload = {
                    "contents": [{
                        "parts": [{"text": prompt}]
                    }]
                }
                
                response = requests.post(
                    url,
                    json=payload,
                    timeout=self.config.REQUEST_TIMEOUT
                )
                
                if response.status_code == 200:
                    self.stats["successful_requests"] += 1
                    return response.text, True, ""
                
                elif response.status_code == 429:  # Rate limit
                    print("⚠️ محدودیت نرخ درخواست. تغییر کلید API...")
                    if not self.rotate_api_key():
                        break
                    time.sleep(2)
                    continue
                
                else:
                    if not self.rotate_api_key():
                        break
                    continue
                    
            except Exception as e:
                if attempt == self.config.MAX_RETRIES - 1:
                    self.stats["failed_requests"] += 1
                    return None, False, f"خطا در درخواست: {str(e)}"
                time.sleep(1)
        
        self.stats["failed_requests"] += 1
        return None, False, "تمام تلاش‌ها ناموفق بود"
    
    def translate_words_batch(self, words_batch: List[Dict], verse_text: str) -> Tuple[List[str], bool, str]:
        """ترجمه دسته‌ای کلمات"""
        # ساخت پرامپت
        prompt = self.build_translation_prompt(words_batch, verse_text)
        
        # ارسال درخواست
        response, success, error = self.make_api_request(prompt)
        
        if not success:
            return [], False, error
        
        # تجزیه پاسخ
        try:
            response_data = json.loads(response)
            content = response_data["candidates"][0]["content"]["parts"][0]["text"]
            
            # استخراج JSON از پاسخ
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = content[start_idx:end_idx]
                translation_data = json.loads(json_str)
                translations = translation_data.get("translations", [])
                
                if len(translations) == len(words_batch):
                    return translations, True, ""
            
            return [], False, "خطا در تجزیه پاسخ"
            
        except Exception as e:
            return [], False, f"خطا در پردازش پاسخ: {str(e)}"
    
    def build_translation_prompt(self, words_batch: List[Dict], verse_text: str) -> str:
        """ساخت پرامپت ترجمه"""
        words_list = []
        for i, word_data in enumerate(words_batch, 1):
            words_list.append(f"{i}. {word_data['arabic']}")
        
        prompt = f"""شما یک متخصص ترجمه قرآن کریم هستید. وظیفه شما ترجمه دقیق کلمات عربی قرآن به زبان آذربایجانی است.

متن کامل آیه: {verse_text}

کلمات برای ترجمه:
{chr(10).join(words_list)}

دستورالعمل‌ها:
1. هر کلمه را به صورت مستقل ترجمه کنید
2. معنای دقیق و مناسب در زمینه قرآنی ارائه دهید
3. از اصطلاحات مذهبی آذربایجانی استفاده کنید
4. پاسخ را فقط در قالب JSON زیر ارائه دهید:

{{"translations": ["ترجمه کلمه 1", "ترجمه کلمه 2", ...]}}

فقط JSON را برگردانید، هیچ توضیح اضافی ندهید."""
        
        return prompt


class StandaloneTranslator:
    """مترجم مستقل"""
    
    def __init__(self, config: StandaloneTranslationConfig):
        self.config = config
        self.client = StandaloneGeminiClient(config)
        self.progress = {
            "last_processed_index": -1,
            "total_processed": 0,
            "successful_translations": 0,
            "failed_translations": 0,
            "start_time": datetime.now().isoformat(),
            "last_update": datetime.now().isoformat(),
            "test_mode": config.TEST_MODE
        }
        self.load_progress()
    
    def load_progress(self):
        """بارگذاری پیشرفت"""
        progress_file = self.config.get_progress_file_path()
        if os.path.exists(progress_file):
            try:
                with open(progress_file, 'r', encoding='utf-8') as f:
                    self.progress.update(json.load(f))
            except Exception:
                pass
    
    def save_progress(self):
        """ذخیره پیشرفت"""
        self.progress["last_update"] = datetime.now().isoformat()
        progress_file = self.config.get_progress_file_path()
        
        try:
            with open(progress_file, 'w', encoding='utf-8') as f:
                json.dump(self.progress, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ خطا در ذخیره پیشرفت: {e}")
    
    def translate_all_words(self, source_data: Dict) -> bool:
        """ترجمه تمام کلمات"""
        verses = source_data.get("verses", [])
        
        if self.config.TEST_MODE:
            verses = verses[:self.config.TEST_WORD_LIMIT]
        
        print(f"🚀 شروع ترجمه کلمه به کلمه قرآن کریم")
        print(f"🔧 حالت: {'تست' if self.config.TEST_MODE else 'کامل'}")
        print(f"📊 تعداد کلیدهای API: {len(self.client.api_keys)}")
        print(f"📦 اندازه دسته: {self.config.BATCH_SIZE}")
        print(f"📁 فایل خروجی: {self.config.get_output_file_path()}")
        print("-" * 60)
        
        # تست کلیدهای API
        print("🔑 تست کلیدهای API...")
        valid_keys = 0
        for i, key in enumerate(self.client.api_keys):
            if self.client.test_api_key(key):
                print(f"   تست کلید #{i+1}: {key[:20]}... ✅ فعال")
                valid_keys += 1
            else:
                print(f"   تست کلید #{i+1}: {key[:20]}... ❌ غیرفعال")
        
        if valid_keys == 0:
            print("❌ هیچ کلید API معتبری یافت نشد!")
            return False
        
        print(f"✅ {valid_keys} کلید API معتبر یافت شد")
        
        if self.config.TEST_MODE:
            print(f"🧪 حالت تست: محدود به {self.config.TEST_WORD_LIMIT} کلمه")
        
        print(f"📖 تعداد کل کلمات: {len(verses)}")
        print(f"🔄 شروع از ایندکس: {self.progress['last_processed_index'] + 1}")
        print()
        
        # شروع ترجمه
        translated_verses = []
        start_index = self.progress["last_processed_index"] + 1
        
        for i in range(start_index, len(verses), self.config.BATCH_SIZE):
            batch_end = min(i + self.config.BATCH_SIZE, len(verses))
            batch = verses[i:batch_end]
            
            print(f"🔄 پردازش دسته {i}-{batch_end-1} ({len(batch)} کلمه)")
            
            # ترجمه دسته
            verse_text = self.get_verse_text(batch[0])
            translations, success, error = self.client.translate_words_batch(batch, verse_text)
            
            if success and len(translations) == len(batch):
                # ذخیره ترجمه‌ها
                for j, word_data in enumerate(batch):
                    translated_word = {
                        "surah_number": word_data["surah_number"],
                        "ayah_number": word_data["ayah_number"],
                        "arabic": word_data["arabic"],
                        "azerbaijani": translations[j],
                        "ayah_words": word_data["ayah_words"],
                        "order": word_data["order"]
                    }
                    translated_verses.append(translated_word)
                
                self.progress["successful_translations"] += len(batch)
            else:
                print(f"❌ خطا در ترجمه دسته: {error}")
                self.progress["failed_translations"] += len(batch)
            
            # بروزرسانی پیشرفت
            self.progress["last_processed_index"] = batch_end - 1
            self.progress["total_processed"] = batch_end
            self.save_progress()
            
            # نمایش پیشرفت
            progress_percent = (batch_end / len(verses)) * 100
            print(f"✅ پیشرفت: {progress_percent:.1f}% ({batch_end}/{len(verses)})")
            print(f"📊 موفق: {self.progress['successful_translations']}, ناموفق: {self.progress['failed_translations']}")
            print()
            
            # تأخیر بین دسته‌ها
            if i + self.config.BATCH_SIZE < len(verses):
                time.sleep(self.config.BATCH_DELAY)
        
        # ذخیره نتایج نهایی
        if translated_verses:
            output_data = {"verses": translated_verses}
            output_file = self.config.get_output_file_path()
            
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                print(f"💾 نتایج در فایل ذخیره شد: {output_file}")
            except Exception as e:
                print(f"❌ خطا در ذخیره نتایج: {e}")
                return False
        
        # گزارش نهایی
        print("=" * 60)
        print("📋 گزارش نهایی:")
        print("-" * 30)
        print(f"📖 کل کلمات: {len(verses)}")
        print(f"✅ پردازش شده: {self.progress['total_processed']}")
        print(f"🎯 موفق: {self.progress['successful_translations']}")
        print(f"❌ ناموفق: {self.progress['failed_translations']}")
        print(f"🧪 حالت تست: {'بله' if self.config.TEST_MODE else 'خیر'}")
        print(f"📁 فایل خروجی: {self.config.get_output_file_path()}")
        print()
        print("🔗 آمار API:")
        print(f"   درخواست‌ها: {self.client.stats['total_requests']}")
        success_rate = (self.client.stats['successful_requests'] / max(1, self.client.stats['total_requests'])) * 100
        print(f"   نرخ موفقیت: {success_rate:.1f}%")
        print(f"   چرخش کلید: {self.client.stats['api_key_rotations']}")
        print("=" * 60)
        
        return True
    
    def get_verse_text(self, word_data: Dict) -> str:
        """دریافت متن آیه"""
        return f"سوره {word_data['surah_number']}, آیه {word_data['ayah_number']}"


def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(description="مترجم مستقل کلمه به کلمه قرآن کریم")
    parser.add_argument('--test', action='store_true', help='حالت تست')
    parser.add_argument('--max-words', type=int, help='حداکثر کلمات')
    parser.add_argument('--api-keys', type=str, help='کلیدهای API')
    
    args = parser.parse_args()
    
    # تنظیم پیکربندی
    config = StandaloneTranslationConfig()
    if args.test:
        config.TEST_MODE = True
    if args.max_words:
        config.TEST_WORD_LIMIT = args.max_words
    if args.api_keys:
        config.DEFAULT_API_KEYS = args.api_keys.split(',')
    
    # بارگذاری داده‌های منبع
    source_file = os.path.join(
        os.path.dirname(os.path.dirname(__file__)),
        'management', 'commands', 'main.json'
    )
    
    if not os.path.exists(source_file):
        print(f"❌ فایل منبع یافت نشد: {source_file}")
        sys.exit(1)
    
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            source_data = json.load(f)
    except Exception as e:
        print(f"❌ خطا در بارگذاری فایل منبع: {e}")
        sys.exit(1)
    
    # اجرای ترجمه
    translator = StandaloneTranslator(config)
    success = translator.translate_all_words(source_data)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

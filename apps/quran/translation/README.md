# ماژول ترجمه کلمه به کلمه قرآن کریم

این ماژول برای ترجمه کلمه به کلمه قرآن کریم از انگلیسی به آذربایجانی با استفاده از Google Gemini API طراحی شده است.

## ویژگی‌ها

- ✅ **ترجمه حرفه‌ای**: استفاده از Google Gemini 2.0-flash-lite
- ✅ **حالت تست**: امکان تست با تعداد محدود کلمات
- ✅ **چرخش کلید API**: پشتیبانی از چندین کلید API
- ✅ **ردیابی پیشرفت**: ذخیره و بازیابی پیشرفت
- ✅ **مدیریت خطا**: مدیریت خطاها و تلاش مجدد
- ✅ **لاگ کامل**: ثبت تمام درخواست‌ها و پاسخ‌ها
- ✅ **تست یونیت**: تست‌های جامع برای تمام اجزا

## ساختار فایل‌ها

```
apps/quran/translation/
├── __init__.py              # مقداردهی ماژول
├── config.py                # تنظیمات و پیکربندی
├── prompts.py               # پرامپت‌های حرفه‌ای
├── client.py                # کلاینت Gemini API
├── translator.py            # کلاس اصلی ترجمه
├── runner.py                # اسکریپت اجرای اصلی
├── tests.py                 # تست‌های یونیت
├── README.md                # این فایل
└── data/                    # دایرکتوری داده‌ها
    ├── main_az.json         # خروجی نهایی (حالت عادی)
    ├── translation_progress.json  # پیشرفت ترجمه
    ├── translation_requests.json  # لاگ درخواست‌ها
    └── test/                # فایل‌های حالت تست
        ├── main_az_test.json
        ├── translation_progress_test.json
        └── translation_requests_test.json
```

## نحوه استفاده

### 1. اجرا در حالت تست
```bash
python apps/quran/translation/runner.py --test
```

### 2. اجرا با کلیدهای API سفارشی
```bash
python apps/quran/translation/runner.py --api-keys "key1,key2,key3"
```

### 3. اجرا با محدودیت تعداد کلمات
```bash
python apps/quran/translation/runner.py --max-words 1000
```

### 4. نمایش آمار فعلی
```bash
python apps/quran/translation/runner.py --stats
```

### 5. ریست پیشرفت
```bash
python apps/quran/translation/runner.py --reset
```

### 6. راهنمای تفصیلی
```bash
python apps/quran/translation/runner.py --help-detailed
```

## اجرای تست‌ها

### تست تمام اجزا
```bash
python -m unittest apps.quran.translation.tests -v
```

### تست اجزای خاص
```bash
# تست تنظیمات
python -m unittest apps.quran.translation.tests.TestTranslationConfig -v

# تست کلاینت API
python -m unittest apps.quran.translation.tests.TestGeminiTranslationClient -v

# تست پرامپت‌ها
python -m unittest apps.quran.translation.tests.TestQuranTranslationPrompts -v

# تست مترجم اصلی
python -m unittest apps.quran.translation.tests.TestQuranWordTranslator -v
```

## تنظیمات

### کلیدهای API پیش‌فرض
کلیدهای API در فایل `config.py` تعریف شده‌اند:

```python
DEFAULT_API_KEYS = [
    "AIzaSyBz8OtW_zJPRiDA4YxTOBxaw2sv3oU6gwY",
    "AIzaSyBJv9CfDRbCJu4uvFXUHEKNSSTFO47IebA",
    "AIzaSyDXdorZ9JrLYkhUJhb-LwXD9e_S-nVD_0w",
    "AIzaSyD7zMokI5CYuN3SDAGxo7s5zQbbnD6qIL8",
    "AIzaSyBZ7t5NhJl2XiCv6EuuzHxTJmtJyCZvfqA"
]
```

### تنظیمات قابل تغییر
- `BATCH_SIZE`: اندازه دسته (پیش‌فرض: 10)
- `MAX_RETRIES`: حداکثر تلاش مجدد (پیش‌فرض: 3)
- `REQUEST_TIMEOUT`: timeout درخواست (پیش‌فرض: 30 ثانیه)
- `BATCH_DELAY`: تأخیر بین دسته‌ها (پیش‌فرض: 2 ثانیه)

## ساختار داده‌ها

### فایل ورودی (main.json)
```json
{
  "verses": [
    {
      "surah_number": 1,
      "ayah_number": 1,
      "arabic": "بِسۡمِ",
      "english": "In (the) name",
      "ayah_words": 4,
      "order": 1
    }
  ]
}
```

### فایل خروجی (main_az.json)
```json
{
  "verses": [
    {
      "surah_number": 1,
      "ayah_number": 1,
      "arabic": "بِسمِ",
      "azerbaijani": "adı ilə",
      "ayah_words": 4,
      "order": 1
    }
  ]
}
```

## مدیریت خطا

- **Rate Limit**: چرخش خودکار کلید API
- **Invalid API Key**: تغییر به کلید بعدی
- **Network Error**: تلاش مجدد با تأخیر
- **Parse Error**: ثبت خطا و ادامه فرآیند

## آمار و گزارش‌گیری

سیستم آمار کاملی از عملکرد ارائه می‌دهد:
- تعداد کل درخواست‌ها
- نرخ موفقیت
- تعداد چرخش کلیدهای API
- تعداد کلمات ترجمه شده
- زمان شروع و پایان

## نکات مهم

1. **حالت تست**: همیشه قبل از اجرای کامل، در حالت تست آزمایش کنید
2. **کلیدهای API**: اطمینان حاصل کنید کلیدهای API معتبر هستند
3. **فضای دیسک**: برای 73,063 کلمه حدود 50MB فضای دیسک نیاز است
4. **زمان اجرا**: ترجمه کامل حدود 3-4 ساعت طول می‌کشد
5. **قطع و وصل**: می‌توانید فرآیند را قطع کرده و بعداً ادامه دهید

## مثال کامل

```bash
# 1. تست کلیدهای API
python apps/quran/translation/runner.py --test

# 2. اجرای کامل
python apps/quran/translation/runner.py

# 3. بررسی آمار
python apps/quran/translation/runner.py --stats

# 4. در صورت نیاز، ریست و شروع مجدد
python apps/quran/translation/runner.py --reset
```

## پشتیبانی

برای گزارش مشکلات یا پیشنهادات، با تیم توسعه تماس بگیرید.

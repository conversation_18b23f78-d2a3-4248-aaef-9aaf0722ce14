"""
تست‌های یونیت برای ماژول ترجمه کلمه به کلمه قرآن
"""

import os
import json
import unittest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import shutil
from typing import Dict, List

from apps.quran.translation.config import TranslationConfig
from apps.quran.translation.client import GeminiTranslationClient
from apps.quran.translation.translator import QuranWordTranslator
from apps.quran.translation.prompts import QuranTranslationPrompts


class TestTranslationConfig(unittest.TestCase):
    """تست تنظیمات ترجمه"""

    def test_config_initialization(self):
        """تست مقداردهی اولیه تنظیمات"""
        self.assertEqual(TranslationConfig.GEMINI_MODEL, "gemini-2.0-flash-lite")
        self.assertEqual(TranslationConfig.BATCH_SIZE, 10)
        self.assertEqual(TranslationConfig.MAX_RETRIES, 3)
        self.assertIsInstance(TranslationConfig.DEFAULT_API_KEYS, list)

    def test_api_url_generation(self):
        """تست تولید URL API"""
        test_key = "test_api_key_123"
        expected_url = f"{TranslationConfig.API_BASE_URL}/{TranslationConfig.GEMINI_MODEL}:generateContent?key={test_key}"
        actual_url = TranslationConfig.get_api_url(test_key)
        self.assertEqual(actual_url, expected_url)

    def test_test_mode_configuration(self):
        """تست تنظیم حالت تست"""
        # ذخیره تنظیمات اصلی
        original_test_mode = TranslationConfig.TEST_MODE
        original_output_file = TranslationConfig.OUTPUT_FILE

        try:
            # فعال کردن حالت تست
            TranslationConfig.set_test_mode(True)
            self.assertTrue(TranslationConfig.TEST_MODE)
            self.assertIn("test", TranslationConfig.OUTPUT_FILE)

            # غیرفعال کردن حالت تست
            TranslationConfig.set_test_mode(False)
            self.assertFalse(TranslationConfig.TEST_MODE)

        finally:
            # بازگردانی تنظیمات اصلی
            TranslationConfig.TEST_MODE = original_test_mode
            TranslationConfig.OUTPUT_FILE = original_output_file


class TestQuranTranslationPrompts(unittest.TestCase):
    """تست پرامپت‌های ترجمه"""

    def test_translation_prompt_generation(self):
        """تست تولید پرامپت ترجمه"""
        words_batch = [
            {"arabic": "بِسۡمِ", "english": "In (the) name"},
            {"arabic": "ٱللَّهِ", "english": "(of) Allah"}
        ]
        verse_text = "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ"

        prompt = QuranTranslationPrompts.build_translation_prompt(words_batch, verse_text)

        # بررسی وجود عناصر کلیدی در پرامپت
        self.assertIn("مترجم حرفه‌ای", prompt)
        self.assertIn("آذربایجانی", prompt)
        self.assertIn(verse_text, prompt)
        self.assertIn("بِسۡمِ", prompt)
        self.assertIn("ٱللَّهِ", prompt)
        self.assertIn("JSON", prompt)
        self.assertIn("translations", prompt)

    def test_empty_words_handling(self):
        """تست مدیریت کلمات خالی"""
        words_batch = [
            {"arabic": "", "english": "empty word"},
            {"arabic": "ٱللَّهِ", "english": "(of) Allah"}
        ]
        verse_text = "test verse"

        prompt = QuranTranslationPrompts.build_translation_prompt(words_batch, verse_text)

        self.assertIn("[خالی]", prompt)
        self.assertIn("ٱللَّهِ", prompt)


class TestGeminiTranslationClient(unittest.TestCase):
    """تست کلاینت ترجمه Gemini"""

    def setUp(self):
        """تنظیمات اولیه تست"""
        self.test_api_keys = ["test_key_1", "test_key_2", "test_key_3"]
        self.client = GeminiTranslationClient(self.test_api_keys)

    def test_client_initialization(self):
        """تست مقداردهی کلاینت"""
        self.assertEqual(self.client.api_keys, self.test_api_keys)
        self.assertEqual(self.client.current_key_index, 0)
        self.assertIsInstance(self.client.stats, dict)

    def test_api_key_rotation(self):
        """تست چرخش کلید API"""
        initial_index = self.client.current_key_index

        # چرخش اول
        result = self.client.rotate_api_key()
        self.assertTrue(result)
        self.assertEqual(self.client.current_key_index, 1)

        # چرخش دوم
        result = self.client.rotate_api_key()
        self.assertTrue(result)
        self.assertEqual(self.client.current_key_index, 2)

        # چرخش سوم (برگشت به اول)
        result = self.client.rotate_api_key()
        self.assertEqual(self.client.current_key_index, 0)

    def test_stats_tracking(self):
        """تست ردیابی آمار"""
        initial_stats = self.client.get_stats()

        # بررسی فیلدهای آمار
        required_fields = [
            "total_requests", "successful_requests", "failed_requests",
            "api_key_rotations", "total_words_translated", "success_rate"
        ]

        for field in required_fields:
            self.assertIn(field, initial_stats)

        # تست ریست آمار
        self.client.stats["total_requests"] = 10
        self.client.reset_stats()
        self.assertEqual(self.client.stats["total_requests"], 0)

    @patch('requests.post')
    def test_successful_api_request(self, mock_post):
        """تست درخواست موفق API"""
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "candidates": [{
                "content": {
                    "parts": [{"text": '{"translations": ["test_translation"]}'}]
                }
            }]
        }
        mock_post.return_value = mock_response

        # تست درخواست
        response, success, error = self.client.make_api_request("test prompt")

        self.assertTrue(success)
        self.assertIn("translations", response)
        self.assertEqual(error, "")
        self.assertEqual(self.client.stats["successful_requests"], 1)

    @patch('requests.post')
    def test_failed_api_request_with_rotation(self, mock_post):
        """تست درخواست ناموفق با چرخش کلید"""
        # Mock response برای rate limit
        mock_response = Mock()
        mock_response.status_code = 429
        mock_post.return_value = mock_response

        # تست درخواست
        response, success, error = self.client.make_api_request("test prompt")

        self.assertFalse(success)
        self.assertIsNone(response)
        # بررسی که پیام خطا شامل "تلاش" یا "محدود" باشد
        self.assertTrue("تلاش" in error or "محدود" in error or len(error) > 0)
        # بررسی که آمار چرخش کلید افزایش یافته
        self.assertGreater(self.client.stats["api_key_rotations"], 0)

    def test_translation_response_parsing(self):
        """تست تجزیه پاسخ ترجمه"""
        # پاسخ معتبر
        valid_response = '{"translations": ["ترجمه1", "ترجمه2", "ترجمه3"]}'
        translations, success = self.client.parse_translation_response(valid_response, 3)

        self.assertTrue(success)
        self.assertEqual(len(translations), 3)
        self.assertEqual(translations[0], "ترجمه1")

        # پاسخ نامعتبر
        invalid_response = "این یک JSON معتبر نیست"
        translations, success = self.client.parse_translation_response(invalid_response, 2)

        self.assertFalse(success)
        self.assertEqual(len(translations), 2)
        self.assertEqual(translations[0], "")

    def test_words_batch_translation(self):
        """تست ترجمه دسته کلمات"""
        words_batch = [
            {"arabic": "بِسۡمِ", "english": "In (the) name"},
            {"arabic": "ٱللَّهِ", "english": "(of) Allah"}
        ]
        verse_text = "بِسۡمِ ٱللَّهِ"

        # Mock کردن درخواست API
        with patch.object(self.client, 'make_api_request') as mock_request:
            mock_request.return_value = ('{"translations": ["adı ilə", "Allahın"]}', True, "")

            translations, success, error = self.client.translate_words_batch(words_batch, verse_text)

            self.assertTrue(success)
            self.assertEqual(len(translations), 2)
            self.assertEqual(translations[0], "adı ilə")
            self.assertEqual(translations[1], "Allahın")


class TestQuranWordTranslator(unittest.TestCase):
    """تست کلاس اصلی ترجمه"""

    def setUp(self):
        """تنظیمات اولیه تست"""
        # ایجاد دایرکتوری موقت برای تست
        self.test_dir = tempfile.mkdtemp()

        # تنظیم حالت تست
        TranslationConfig.set_test_mode(True)
        TranslationConfig.DATA_DIR = self.test_dir
        TranslationConfig.OUTPUT_FILE = os.path.join(self.test_dir, "test_output.json")
        TranslationConfig.PROGRESS_FILE = os.path.join(self.test_dir, "test_progress.json")
        TranslationConfig.REQUESTS_LOG_FILE = os.path.join(self.test_dir, "test_requests.json")

        # ایجاد مترجم تست
        self.translator = QuranWordTranslator(["test_key"], test_mode=True)

    def tearDown(self):
        """پاکسازی پس از تست"""
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_translator_initialization(self):
        """تست مقداردهی مترجم"""
        self.assertIsInstance(self.translator.client, GeminiTranslationClient)
        self.assertIsInstance(self.translator.progress, dict)
        self.assertIsInstance(self.translator.requests_log, list)

    def test_progress_save_and_load(self):
        """تست ذخیره و بارگذاری پیشرفت"""
        # تغییر پیشرفت
        self.translator.progress["test_field"] = "test_value"
        self.translator.progress["successful_translations"] = 5

        # ذخیره
        self.translator.save_progress()

        # بارگذاری مجدد
        new_translator = QuranWordTranslator(["test_key"], test_mode=True)

        self.assertEqual(new_translator.progress["test_field"], "test_value")
        self.assertEqual(new_translator.progress["successful_translations"], 5)

    def test_request_log_functionality(self):
        """تست عملکرد لاگ درخواست‌ها"""
        # اضافه کردن لاگ
        test_log = {
            "timestamp": "2024-01-01T10:00:00",
            "surah": 1,
            "ayah": 1,
            "success": True,
            "words": ["test_word"],
            "translations": ["test_translation"]
        }

        self.translator.save_request_log(test_log)

        # بررسی ذخیره
        self.assertEqual(len(self.translator.requests_log), 1)
        self.assertEqual(self.translator.requests_log[0]["surah"], 1)

        # بارگذاری مجدد
        new_translator = QuranWordTranslator(["test_key"], test_mode=True)
        self.assertEqual(len(new_translator.requests_log), 1)

    def test_arabic_text_correction(self):
        """تست تصحیح متن عربی"""
        test_cases = [
            ("ٱللَّهِ", "اللَّهِ"),  # تبدیل ٱ به ا
            ("بِسۡمِ", "بِسمِ"),      # حذف علامت سکون
            ("ٱلرَّحۡمَٰنِ", "الرَّحمَٰنِ"),  # ترکیب هر دو
            ("", ""),                # متن خالی
            ("  test  ", "test")     # حذف فاصله‌های اضافی
        ]

        for input_text, expected_output in test_cases:
            result = self.translator.detailed_correct_arabic_text(input_text)
            self.assertEqual(result, expected_output, f"Failed for input: {input_text}")

    @patch('apps.quran.models.QuranSura.objects.get')
    @patch('apps.quran.models.QuranVerse.objects.get')
    def test_verse_text_retrieval(self, mock_verse_get, mock_sura_get):
        """تست دریافت متن آیه"""
        # Mock objects
        mock_sura = Mock()
        mock_verse = Mock()
        mock_verse.text = "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ"

        mock_sura_get.return_value = mock_sura
        mock_verse_get.return_value = mock_verse

        # تست در حالت عادی (غیر تست)
        self.translator.config.TEST_MODE = False
        result = self.translator.get_verse_text(1, 1)

        self.assertEqual(result, "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ")
        mock_sura_get.assert_called_once_with(index=1)
        mock_verse_get.assert_called_once_with(sura=mock_sura, number_in_surah=1)

        # تست در حالت تست
        self.translator.config.TEST_MODE = True
        result = self.translator.get_verse_text(2, 3)

        self.assertIn("تست", result)
        self.assertIn("2:3", result)

    def test_batch_translation(self):
        """تست ترجمه دسته‌ای"""
        words_batch = [
            {
                'surah_number': 1,
                'ayah_number': 1,
                'arabic': 'بِسۡمِ',
                'english': 'In (the) name'
            }
        ]

        # Mock کردن کلاینت
        with patch.object(self.translator.client, 'translate_words_batch') as mock_translate:
            mock_translate.return_value = (["adı ilə"], True, "")

            result = self.translator.translate_batch(words_batch)

            self.assertEqual(len(result), 1)
            self.assertEqual(result[0], "adı ilə")

            # بررسی ذخیره لاگ
            self.assertEqual(len(self.translator.requests_log), 1)
            log_entry = self.translator.requests_log[0]
            self.assertEqual(log_entry["surah"], 1)
            self.assertEqual(log_entry["ayah"], 1)
            self.assertTrue(log_entry["success"])


if __name__ == '__main__':
    # اجرای تست‌ها
    unittest.main(verbosity=2)


class TestTranslationConfig(unittest.TestCase):
    """تست تنظیمات ترجمه"""
    
    def test_config_initialization(self):
        """تست مقداردهی اولیه تنظیمات"""
        self.assertEqual(TranslationConfig.GEMINI_MODEL, "gemini-2.0-flash-lite")
        self.assertEqual(TranslationConfig.BATCH_SIZE, 10)
        self.assertEqual(TranslationConfig.MAX_RETRIES, 3)
        self.assertIsInstance(TranslationConfig.DEFAULT_API_KEYS, list)
    
    def test_api_url_generation(self):
        """تست تولید URL API"""
        test_key = "test_api_key_123"
        expected_url = f"{TranslationConfig.API_BASE_URL}/{TranslationConfig.GEMINI_MODEL}:generateContent?key={test_key}"
        actual_url = TranslationConfig.get_api_url(test_key)
        self.assertEqual(actual_url, expected_url)
    
    def test_test_mode_configuration(self):
        """تست تنظیم حالت تست"""
        # ذخیره تنظیمات اصلی
        original_test_mode = TranslationConfig.TEST_MODE
        original_output_file = TranslationConfig.OUTPUT_FILE
        
        try:
            # فعال کردن حالت تست
            TranslationConfig.set_test_mode(True)
            self.assertTrue(TranslationConfig.TEST_MODE)
            self.assertIn("test", TranslationConfig.OUTPUT_FILE)
            
            # غیرفعال کردن حالت تست
            TranslationConfig.set_test_mode(False)
            self.assertFalse(TranslationConfig.TEST_MODE)
        
        finally:
            # بازگردانی تنظیمات اصلی
            TranslationConfig.TEST_MODE = original_test_mode
            TranslationConfig.OUTPUT_FILE = original_output_file


class TestQuranTranslationPrompts(unittest.TestCase):
    """تست پرامپت‌های ترجمه"""
    
    def test_translation_prompt_generation(self):
        """تست تولید پرامپت ترجمه"""
        words_batch = [
            {"arabic": "بِسۡمِ", "english": "In (the) name"},
            {"arabic": "ٱللَّهِ", "english": "(of) Allah"}
        ]
        verse_text = "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ"
        
        prompt = QuranTranslationPrompts.build_translation_prompt(words_batch, verse_text)
        
        # بررسی وجود عناصر کلیدی در پرامپت
        self.assertIn("مترجم حرفه‌ای", prompt)
        self.assertIn("آذربایجانی", prompt)
        self.assertIn(verse_text, prompt)
        self.assertIn("بِسۡمِ", prompt)
        self.assertIn("ٱللَّهِ", prompt)
        self.assertIn("JSON", prompt)
        self.assertIn("translations", prompt)
    
    def test_empty_words_handling(self):
        """تست مدیریت کلمات خالی"""
        words_batch = [
            {"arabic": "", "english": "empty word"},
            {"arabic": "ٱللَّهِ", "english": "(of) Allah"}
        ]
        verse_text = "test verse"
        
        prompt = QuranTranslationPrompts.build_translation_prompt(words_batch, verse_text)
        
        self.assertIn("[خالی]", prompt)
        self.assertIn("ٱللَّهِ", prompt)
    
    def test_test_prompt(self):
        """تست پرامپت تست"""
        test_prompt = QuranTranslationPrompts.build_test_prompt()
        
        self.assertIn("سلام", test_prompt)
        self.assertIn("آذربایجانی", test_prompt)
        self.assertIn("JSON", test_prompt)


class TestGeminiTranslationClient(unittest.TestCase):
    """تست کلاینت ترجمه Gemini"""
    
    def setUp(self):
        """تنظیمات اولیه تست"""
        self.test_api_keys = ["test_key_1", "test_key_2", "test_key_3"]
        self.client = GeminiTranslationClient(self.test_api_keys)
    
    def test_client_initialization(self):
        """تست مقداردهی کلاینت"""
        self.assertEqual(self.client.api_keys, self.test_api_keys)
        self.assertEqual(self.client.current_key_index, 0)
        self.assertIsInstance(self.client.stats, dict)
    
    def test_api_key_rotation(self):
        """تست چرخش کلید API"""
        initial_index = self.client.current_key_index
        
        # چرخش اول
        result = self.client.rotate_api_key()
        self.assertTrue(result)
        self.assertEqual(self.client.current_key_index, 1)
        
        # چرخش دوم
        result = self.client.rotate_api_key()
        self.assertTrue(result)
        self.assertEqual(self.client.current_key_index, 2)
        
        # چرخش سوم (برگشت به اول)
        result = self.client.rotate_api_key()
        self.assertEqual(self.client.current_key_index, 0)
    
    def test_stats_tracking(self):
        """تست ردیابی آمار"""
        initial_stats = self.client.get_stats()
        
        # بررسی فیلدهای آمار
        required_fields = [
            "total_requests", "successful_requests", "failed_requests",
            "api_key_rotations", "total_words_translated", "success_rate"
        ]
        
        for field in required_fields:
            self.assertIn(field, initial_stats)
        
        # تست ریست آمار
        self.client.stats["total_requests"] = 10
        self.client.reset_stats()
        self.assertEqual(self.client.stats["total_requests"], 0)
    
    @patch('requests.post')
    def test_successful_api_request(self, mock_post):
        """تست درخواست موفق API"""
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "candidates": [{
                "content": {
                    "parts": [{"text": '{"translations": ["test_translation"]}'}]
                }
            }]
        }
        mock_post.return_value = mock_response
        
        # تست درخواست
        response, success, error = self.client.make_api_request("test prompt")
        
        self.assertTrue(success)
        self.assertIn("translations", response)
        self.assertEqual(error, "")
        self.assertEqual(self.client.stats["successful_requests"], 1)
    
    @patch('requests.post')
    def test_failed_api_request_with_rotation(self, mock_post):
        """تست درخواست ناموفق با چرخش کلید"""
        # Mock response برای rate limit
        mock_response = Mock()
        mock_response.status_code = 429
        mock_post.return_value = mock_response

        # تست درخواست
        response, success, error = self.client.make_api_request("test prompt")
        
        self.assertFalse(success)
        self.assertIsNone(response)
        # بررسی که پیام خطا شامل "تلاش" یا "محدود" باشد
        self.assertTrue("تلاش" in error or "محدود" in error or len(error) > 0)
        # بررسی که آمار چرخش کلید افزایش یافته
        self.assertGreater(self.client.stats["api_key_rotations"], 0)
    
    def test_translation_response_parsing(self):
        """تست تجزیه پاسخ ترجمه"""
        # پاسخ معتبر
        valid_response = '{"translations": ["ترجمه1", "ترجمه2", "ترجمه3"]}'
        translations, success = self.client.parse_translation_response(valid_response, 3)
        
        self.assertTrue(success)
        self.assertEqual(len(translations), 3)
        self.assertEqual(translations[0], "ترجمه1")
        
        # پاسخ نامعتبر
        invalid_response = "این یک JSON معتبر نیست"
        translations, success = self.client.parse_translation_response(invalid_response, 2)
        
        self.assertFalse(success)
        self.assertEqual(len(translations), 2)
        self.assertEqual(translations[0], "")
    
    def test_words_batch_translation(self):
        """تست ترجمه دسته کلمات"""
        words_batch = [
            {"arabic": "بِسۡمِ", "english": "In (the) name"},
            {"arabic": "ٱللَّهِ", "english": "(of) Allah"}
        ]
        verse_text = "بِسۡمِ ٱللَّهِ"
        
        # Mock کردن درخواست API
        with patch.object(self.client, 'make_api_request') as mock_request:
            mock_request.return_value = ('{"translations": ["adı ilə", "Allahın"]}', True, "")
            
            translations, success, error = self.client.translate_words_batch(words_batch, verse_text)
            
            self.assertTrue(success)
            self.assertEqual(len(translations), 2)
            self.assertEqual(translations[0], "adı ilə")
            self.assertEqual(translations[1], "Allahın")


class TestQuranWordTranslator(unittest.TestCase):
    """تست کلاس اصلی ترجمه"""
    
    def setUp(self):
        """تنظیمات اولیه تست"""
        # ایجاد دایرکتوری موقت برای تست
        self.test_dir = tempfile.mkdtemp()
        
        # تنظیم حالت تست
        TranslationConfig.set_test_mode(True)
        TranslationConfig.DATA_DIR = self.test_dir
        TranslationConfig.OUTPUT_FILE = os.path.join(self.test_dir, "test_output.json")
        TranslationConfig.PROGRESS_FILE = os.path.join(self.test_dir, "test_progress.json")
        TranslationConfig.REQUESTS_LOG_FILE = os.path.join(self.test_dir, "test_requests.json")
        
        # ایجاد مترجم تست
        self.translator = QuranWordTranslator(["test_key"], test_mode=True)
    
    def tearDown(self):
        """پاکسازی پس از تست"""
        shutil.rmtree(self.test_dir, ignore_errors=True)
    
    def test_translator_initialization(self):
        """تست مقداردهی مترجم"""
        self.assertIsInstance(self.translator.client, GeminiTranslationClient)
        self.assertIsInstance(self.translator.progress, dict)
        self.assertIsInstance(self.translator.requests_log, list)
    
    def test_progress_save_and_load(self):
        """تست ذخیره و بارگذاری پیشرفت"""
        # تغییر پیشرفت
        self.translator.progress["test_field"] = "test_value"
        self.translator.progress["successful_translations"] = 5
        
        # ذخیره
        self.translator.save_progress()
        
        # بارگذاری مجدد
        new_translator = QuranWordTranslator(["test_key"], test_mode=True)
        
        self.assertEqual(new_translator.progress["test_field"], "test_value")
        self.assertEqual(new_translator.progress["successful_translations"], 5)
    
    def test_request_log_functionality(self):
        """تست عملکرد لاگ درخواست‌ها"""
        # اضافه کردن لاگ
        test_log = {
            "timestamp": "2024-01-01T10:00:00",
            "surah": 1,
            "ayah": 1,
            "success": True,
            "words": ["test_word"],
            "translations": ["test_translation"]
        }
        
        self.translator.save_request_log(test_log)
        
        # بررسی ذخیره
        self.assertEqual(len(self.translator.requests_log), 1)
        self.assertEqual(self.translator.requests_log[0]["surah"], 1)
        
        # بارگذاری مجدد
        new_translator = QuranWordTranslator(["test_key"], test_mode=True)
        self.assertEqual(len(new_translator.requests_log), 1)
    
    def test_arabic_text_correction(self):
        """تست تصحیح متن عربی"""
        test_cases = [
            ("ٱللَّهِ", "اللَّهِ"),  # تبدیل ٱ به ا
            ("بِسۡمِ", "بِسمِ"),      # حذف علامت سکون
            ("ٱلرَّحۡمَٰنِ", "الرَّحمَٰنِ"),  # ترکیب هر دو
            ("", ""),                # متن خالی
            ("  test  ", "test")     # حذف فاصله‌های اضافی
        ]
        
        for input_text, expected_output in test_cases:
            result = self.translator.detailed_correct_arabic_text(input_text)
            self.assertEqual(result, expected_output, f"Failed for input: {input_text}")
    
    @patch('apps.quran.models.QuranSura.objects.get')
    @patch('apps.quran.models.QuranVerse.objects.get')
    def test_verse_text_retrieval(self, mock_verse_get, mock_sura_get):
        """تست دریافت متن آیه"""
        # Mock objects
        mock_sura = Mock()
        mock_verse = Mock()
        mock_verse.text = "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ"
        
        mock_sura_get.return_value = mock_sura
        mock_verse_get.return_value = mock_verse
        
        # تست در حالت عادی (غیر تست)
        self.translator.config.TEST_MODE = False
        result = self.translator.get_verse_text(1, 1)
        
        self.assertEqual(result, "بِسۡمِ ٱللَّهِ ٱلرَّحۡمَٰنِ ٱلرَّحِيمِ")
        mock_sura_get.assert_called_once_with(index=1)
        mock_verse_get.assert_called_once_with(sura=mock_sura, number_in_surah=1)
        
        # تست در حالت تست
        self.translator.config.TEST_MODE = True
        result = self.translator.get_verse_text(2, 3)
        
        self.assertIn("تست", result)
        self.assertIn("2:3", result)
    
    def test_batch_translation(self):
        """تست ترجمه دسته‌ای"""
        words_batch = [
            {
                'surah_number': 1,
                'ayah_number': 1,
                'arabic': 'بِسۡمِ',
                'english': 'In (the) name'
            }
        ]
        
        # Mock کردن کلاینت
        with patch.object(self.translator.client, 'translate_words_batch') as mock_translate:
            mock_translate.return_value = (["adı ilə"], True, "")
            
            result = self.translator.translate_batch(words_batch)
            
            self.assertEqual(len(result), 1)
            self.assertEqual(result[0], "adı ilə")
            
            # بررسی ذخیره لاگ
            self.assertEqual(len(self.translator.requests_log), 1)
            log_entry = self.translator.requests_log[0]
            self.assertEqual(log_entry["surah"], 1)
            self.assertEqual(log_entry["ayah"], 1)
            self.assertTrue(log_entry["success"])


if __name__ == '__main__':
    # اجرای تست‌ها
    unittest.main(verbosity=2)

#!/usr/bin/env python3
"""
اسکریپت ساده برای اجرای ترجمه کلمه به کلمه قرآن کریم
بدون وابستگی به Django
"""

import os
import sys
import json
import argparse
from datetime import datetime

# اضافه کردن مسیر پروژه
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from apps.quran.translation.config import TranslationConfig
from apps.quran.translation.translator import QuranWordTranslator


def print_header():
    """نمایش هدر برنامه"""
    print("=" * 80)
    print("🕌 مترجم کلمه به کلمه قرآن کریم - زبان آذربایجانی")
    print(f"📅 تاریخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🤖 مدل: Google Gemini 2.0-flash-lite")
    print("=" * 80)
    print()


def load_source_data():
    """بارگذاری داده‌های منبع"""
    source_file = os.path.join(
        os.path.dirname(os.path.dirname(__file__)),
        'management', 'commands', 'main.json'
    )
    
    if not os.path.exists(source_file):
        print(f"❌ فایل منبع یافت نشد: {source_file}")
        return None
    
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ خطا در بارگذاری فایل منبع: {e}")
        return None


def run_translation(test_mode=False, max_words=None, api_keys=None):
    """اجرای فرآیند ترجمه"""
    print_header()
    
    # بارگذاری داده‌های منبع
    print("📖 بارگذاری داده‌های منبع...")
    source_data = load_source_data()
    if not source_data:
        return False
    
    # تنظیم پیکربندی
    config = TranslationConfig()
    if test_mode:
        config.TEST_MODE = True
        config.TEST_WORD_LIMIT = 20
    
    if api_keys:
        config.DEFAULT_API_KEYS = api_keys.split(',')
    
    if max_words:
        config.TEST_WORD_LIMIT = max_words
    
    # ایجاد مترجم
    translator = QuranWordTranslator(config)
    
    print("🚀 شروع فرآیند ترجمه...")
    print("⚠️  برای توقف از Ctrl+C استفاده کنید")
    print("-" * 60)
    
    try:
        # اجرای ترجمه
        success = translator.translate_all_words(source_data)
        
        if success:
            print("\n🎉 ترجمه با موفقیت تکمیل شد!")
            return True
        else:
            print("\n❌ ترجمه با خطا مواجه شد.")
            return False
            
    except KeyboardInterrupt:
        print("\n⏹️  فرآیند توسط کاربر متوقف شد.")
        print("💾 پیشرفت ذخیره شده است. می‌توانید بعداً ادامه دهید.")
        return False
    except Exception as e:
        print(f"\n❌ خطای غیرمنتظره: {e}")
        return False


def show_stats():
    """نمایش آمار فعلی"""
    config = TranslationConfig()
    progress_file = config.get_progress_file_path()
    
    if not os.path.exists(progress_file):
        print("📊 آمار فعلی ترجمه:")
        print("-" * 40)
        print("❌ هیچ پیشرفتی یافت نشد. ترجمه هنوز شروع نشده است.")
        return
    
    try:
        with open(progress_file, 'r', encoding='utf-8') as f:
            progress = json.load(f)
        
        print("📊 آمار فعلی ترجمه:")
        print("-" * 40)
        print(f"📖 کل پردازش شده: {progress.get('total_processed', 0):,}")
        print(f"✅ موفق: {progress.get('successful_translations', 0):,}")
        print(f"❌ ناموفق: {progress.get('failed_translations', 0):,}")
        print(f"📅 آخرین بروزرسانی: {progress.get('last_update', 'نامشخص')}")
        print(f"🧪 حالت تست: {'بله' if progress.get('test_mode', False) else 'خیر'}")
        
    except Exception as e:
        print(f"❌ خطا در خواندن آمار: {e}")


def reset_progress():
    """ریست پیشرفت"""
    config = TranslationConfig()
    
    files_to_remove = [
        config.get_progress_file_path(),
        config.get_requests_log_path(),
        config.get_output_file_path()
    ]
    
    removed_count = 0
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                removed_count += 1
                print(f"🗑️  حذف شد: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"❌ خطا در حذف {file_path}: {e}")
    
    if removed_count > 0:
        print(f"✅ {removed_count} فایل حذف شد. می‌توانید از ابتدا شروع کنید.")
    else:
        print("ℹ️  هیچ فایلی برای حذف یافت نشد.")


def main():
    """تابع اصلی"""
    parser = argparse.ArgumentParser(
        description="مترجم کلمه به کلمه قرآن کریم به آذربایجانی",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--test', action='store_true',
                       help='اجرا در حالت تست (محدود به 20 کلمه)')
    parser.add_argument('--max-words', type=int,
                       help='حداکثر تعداد کلمات برای پردازش')
    parser.add_argument('--api-keys', type=str,
                       help='کلیدهای API سفارشی (جدا شده با کاما)')
    parser.add_argument('--stats', action='store_true',
                       help='نمایش آمار فعلی')
    parser.add_argument('--reset', action='store_true',
                       help='ریست پیشرفت و شروع مجدد')
    
    args = parser.parse_args()
    
    if args.stats:
        show_stats()
        return
    
    if args.reset:
        reset_progress()
        return
    
    # اجرای ترجمه
    success = run_translation(
        test_mode=args.test,
        max_words=args.max_words,
        api_keys=args.api_keys
    )
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

"""
پرامپت‌های بهینه شده برای ترجمه کلمات قرآن
"""

from typing import List, Dict


class QuranTranslationPrompts:
    """کلاس پرامپت‌های ترجمه قرآن"""
    
    @staticmethod
    def build_translation_prompt(words_batch: List[Dict], verse_text: str, target_language: str = "آذربایجانی") -> str:
        """
        ساخت پرامپت بهینه شده برای ترجمه کلمات قرآن
        
        Args:
            words_batch: لیست کلمات برای ترجمه
            verse_text: متن کامل آیه
            target_language: زبان مقصد
            
        Returns:
            پرامپت بهینه شده
        """
        
        # ساخت لیست کلمات با شماره‌گذاری
        words_list = []
        for i, word_data in enumerate(words_batch, 1):
            arabic_word = word_data['arabic'].strip()
            english_word = word_data['english'].strip()
            
            # اگر کلمه خالی است، آن را نشان می‌دهیم
            if not arabic_word:
                words_list.append(f"{i}. [خالی] (انگلیسی: {english_word})")
            else:
                words_list.append(f"{i}. {arabic_word} (انگلیسی: {english_word})")
        
        words_formatted = "\n".join(words_list)
        
        # پرامپت اصلی با اصول prompt engineering
        prompt = f"""# نقش و مسئولیت
شما یک مترجم حرفه‌ای و متخصص در ترجمه متون قرآنی هستید که تخصص ویژه در زبان {target_language} دارید.

# وظیفه
کلمات زیر از قرآن کریم را از عربی به زبان {target_language} ترجمه کنید.

# متن آیه کامل (برای درک بهتر معنا)
```
{verse_text}
```

# کلمات برای ترجمه
{words_formatted}

# دستورالعمل‌های دقیق
1. **دقت در ترجمه**: از معنای دقیق و مناسب برای متون قرآنی استفاده کنید
2. **اصطلاحات مذهبی**: از واژگان مذهبی مناسب در زبان {target_language} استفاده کنید
3. **حفظ ترتیب**: ترتیب ترجمه‌ها باید دقیقاً همان ترتیب کلمات باشد
4. **کلمات خالی**: اگر کلمه‌ای خالی است، آن را خالی نگه دارید
5. **سادگی**: از کلمات ساده و قابل فهم استفاده کنید

# قالب خروجی مورد انتظار
پاسخ خود را دقیقاً در قالب JSON زیر ارائه دهید:

```json
{{
    "translations": [
        "ترجمه کلمه 1",
        "ترجمه کلمه 2",
        "ترجمه کلمه 3",
        ...
    ]
}}
```

# نکات مهم
- فقط JSON را برگردانید، هیچ توضیح اضافی ندهید
- تعداد ترجمه‌ها باید دقیقاً {len(words_batch)} عدد باشد
- از کاراکترهای غیرضروری خودداری کنید
- در صورت عدم اطمینان، از ساده‌ترین معادل استفاده کنید

# شروع ترجمه
"""
        
        return prompt.strip()
    
    @staticmethod
    def build_test_prompt() -> str:
        """پرامپت تست برای بررسی عملکرد API"""
        return """شما یک مترجم حرفه‌ای هستید. لطفاً کلمه "سلام" را به زبان آذربایجانی ترجمه کنید.

پاسخ را در قالب JSON زیر ارائه دهید:
{
    "translation": "ترجمه کلمه"
}

فقط JSON را برگردانید."""
    
    @staticmethod
    def build_validation_prompt(original_word: str, translation: str) -> str:
        """پرامپت اعتبارسنجی ترجمه"""
        return f"""لطفاً کیفیت ترجمه زیر را بررسی کنید:

کلمه عربی: {original_word}
ترجمه آذربایجانی: {translation}

آیا این ترجمه صحیح و مناسب است؟ پاسخ را در قالب JSON ارائه دهید:

{{
    "is_valid": true/false,
    "confidence": 0.0-1.0,
    "suggestion": "پیشنهاد بهتر (در صورت نیاز)"
}}"""
    
    @staticmethod
    def get_system_message() -> str:
        """پیام سیستم برای تنظیم رفتار مدل"""
        return """شما یک مترجم حرفه‌ای متون اسلامی و قرآنی هستید. وظیفه شما ترجمه دقیق و مناسب کلمات قرآن کریم است. همیشه:

1. از معنای دقیق و مناسب استفاده کنید
2. اصطلاحات مذهبی را رعایت کنید  
3. پاسخ‌های خود را در قالب JSON ارائه دهید
4. از توضیحات اضافی خودداری کنید
5. کیفیت و دقت را بر سرعت ترجیح دهید"""

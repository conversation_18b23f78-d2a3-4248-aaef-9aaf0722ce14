"""
کلاینت Gemini برای ترجمه کلمات قرآن
"""

import json
import time
import requests
from typing import List, Dict, Optional, Tuple
from datetime import datetime

from apps.quran.translation.config import TranslationConfig
from apps.quran.translation.prompts import QuranTranslationPrompts


class GeminiTranslationClient:
    """کلاینت ترجمه Gemini با قابلیت چرخش کلید API"""
    
    def __init__(self, api_keys: List[str] = None):
        """
        مقداردهی کلاینت
        
        Args:
            api_keys: لیست کلیدهای API
        """
        self.api_keys = api_keys or TranslationConfig.DEFAULT_API_KEYS
        self.current_key_index = 0
        self.config = TranslationConfig
        self.prompts = QuranTranslationPrompts()
        
        # آمار عملکرد
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "api_key_rotations": 0,
            "total_words_translated": 0,
        }
        
        if not self.api_keys:
            raise ValueError("حداقل یک کلید API مورد نیاز است")
    
    def get_current_api_url(self) -> str:
        """دریافت URL API فعلی"""
        current_key = self.api_keys[self.current_key_index]
        return self.config.get_api_url(current_key)
    
    def rotate_api_key(self) -> bool:
        """
        چرخش به کلید API بعدی
        
        Returns:
            True اگر کلید جدید موجود باشد، False اگر همه کلیدها تمام شده باشد
        """
        old_index = self.current_key_index
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
        self.stats["api_key_rotations"] += 1
        
        # اگر به کلید اول برگشتیم، یعنی همه کلیدها تست شده‌اند
        if self.current_key_index == old_index:
            return False
        
        print(f"🔄 تغییر به کلید API شماره {self.current_key_index + 1}")
        return True
    
    def test_api_key(self, api_key: str) -> bool:
        """
        تست یک کلید API
        
        Args:
            api_key: کلید API برای تست
            
        Returns:
            True اگر کلید معتبر باشد
        """
        test_url = self.config.get_api_url(api_key)
        test_prompt = self.prompts.build_test_prompt()
        
        payload = {
            "contents": [{
                "parts": [{"text": test_prompt}]
            }],
            "generationConfig": {
                "temperature": 0.1,
                "maxOutputTokens": 100,
            }
        }
        
        try:
            response = requests.post(
                test_url, 
                json=payload, 
                headers={"Content-Type": "application/json"}, 
                timeout=30
            )
            print(f'--error-test-api-key--0-> response:{response}/ response.status_code:{response.status_code}')
            return response.status_code == 200
        except Exception as exp:
            print(f'--error-test-api-key--1-> {exp}')
            return False
    
    def test_all_api_keys(self) -> Dict[int, bool]:
        """
        تست تمام کلیدهای API
        
        Returns:
            دیکشنری از ایندکس کلید به وضعیت
        """
        results = {}
        print("🔑 تست کلیدهای API...")
        
        for i, api_key in enumerate(self.api_keys):
            print(f"   تست کلید #{i + 1}: {api_key[:20]}...", end=" ")
            is_valid = self.test_api_key(api_key)
            results[i] = is_valid
            status = "✅ فعال" if is_valid else "❌ غیرفعال"
            print(status)
            time.sleep(1)  # تأخیر کوتاه بین تست‌ها
        
        return results
    
    def make_api_request(self, prompt: str) -> Tuple[Optional[str], bool, str]:
        """
        ارسال درخواست به API
        
        Args:
            prompt: پرامپت برای ارسال
            
        Returns:
            (پاسخ, موفقیت, پیام خطا)
        """
        payload = {
            "contents": [{
                "parts": [{"text": prompt}]
            }],
            "generationConfig": self.config.GENERATION_CONFIG
        }
        
        headers = {"Content-Type": "application/json"}
        
        for attempt in range(self.config.MAX_RETRIES):
            try:
                api_url = self.get_current_api_url()
                
                response = requests.post(
                    api_url, 
                    json=payload, 
                    headers=headers, 
                    timeout=self.config.REQUEST_TIMEOUT
                )
                
                self.stats["total_requests"] += 1
                
                if response.status_code == 200:
                    result = response.json()
                    if 'candidates' in result and result['candidates']:
                        content = result['candidates'][0]['content']['parts'][0]['text']
                        self.stats["successful_requests"] += 1
                        return content, True, ""
                    else:
                        error_msg = f"پاسخ نامعتبر API: {result}"
                        self.stats["failed_requests"] += 1
                        return None, False, error_msg
                
                elif response.status_code == 429:  # Rate limit
                    print(f"⚠️ محدودیت نرخ درخواست. تغییر کلید API...")
                    if not self.rotate_api_key():
                        error_msg = "تمام کلیدهای API محدود شده‌اند"
                        self.stats["failed_requests"] += 1
                        return None, False, error_msg
                    time.sleep(self.config.RETRY_DELAY * (attempt + 1))
                    continue
                
                elif response.status_code == 403:  # Invalid API key
                    print(f"❌ کلید API نامعتبر. تغییر کلید...")
                    if not self.rotate_api_key():
                        error_msg = "تمام کلیدهای API نامعتبر هستند"
                        self.stats["failed_requests"] += 1
                        return None, False, error_msg
                    continue
                
                else:
                    error_msg = f"خطای HTTP {response.status_code}: {response.text}"
                    if attempt < self.config.MAX_RETRIES - 1:
                        time.sleep(self.config.RETRY_DELAY * (attempt + 1))
                        continue
                    else:
                        self.stats["failed_requests"] += 1
                        return None, False, error_msg
                        
            except requests.exceptions.Timeout:
                error_msg = f"Timeout در تلاش {attempt + 1}"
                if attempt < self.config.MAX_RETRIES - 1:
                    time.sleep(self.config.RETRY_DELAY * (attempt + 1))
                    continue
                else:
                    self.stats["failed_requests"] += 1
                    return None, False, error_msg
            
            except requests.exceptions.ConnectionError:
                error_msg = f"خطای اتصال در تلاش {attempt + 1}"
                if attempt < self.config.MAX_RETRIES - 1:
                    time.sleep(self.config.RETRY_DELAY * (attempt + 1))
                    continue
                else:
                    self.stats["failed_requests"] += 1
                    return None, False, error_msg
            
            except Exception as e:
                error_msg = f"خطای غیرمنتظره: {str(e)}"
                if attempt < self.config.MAX_RETRIES - 1:
                    time.sleep(self.config.RETRY_DELAY * (attempt + 1))
                    continue
                else:
                    self.stats["failed_requests"] += 1
                    return None, False, error_msg
        
        self.stats["failed_requests"] += 1
        return None, False, "تمام تلاش‌ها ناموفق بود"
    
    def parse_translation_response(self, response: str, expected_count: int) -> Tuple[List[str], bool]:
        """
        تجزیه پاسخ ترجمه
        
        Args:
            response: پاسخ API
            expected_count: تعداد مورد انتظار ترجمه‌ها
            
        Returns:
            (لیست ترجمه‌ها, موفقیت تجزیه)
        """
        try:
            # استخراج JSON از پاسخ
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start == -1 or json_end == 0:
                print(f"❌ JSON یافت نشد در پاسخ: {response[:100]}...")
                return [""] * expected_count, False
            
            json_str = response[json_start:json_end]
            data = json.loads(json_str)
            
            translations = data.get('translations', [])
            
            # اطمینان از تعداد صحیح
            while len(translations) < expected_count:
                translations.append("")
            
            # برش اضافی‌ها
            translations = translations[:expected_count]
            
            return translations, True
            
        except json.JSONDecodeError as e:
            print(f"❌ خطا در تجزیه JSON: {e}")
            print(f"پاسخ: {response[:200]}...")
            return [""] * expected_count, False
        
        except Exception as e:
            print(f"❌ خطای غیرمنتظره در تجزیه: {e}")
            return [""] * expected_count, False
    
    def get_stats(self) -> Dict:
        """دریافت آمار عملکرد"""
        total = self.stats["total_requests"]
        success_rate = (self.stats["successful_requests"] / total * 100) if total > 0 else 0
        
        return {
            **self.stats,
            "success_rate": round(success_rate, 2),
            "current_api_key_index": self.current_key_index,
            "total_api_keys": len(self.api_keys)
        }
    
    def reset_stats(self):
        """ریست آمار"""
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "api_key_rotations": 0,
            "total_words_translated": 0,
        }

    def translate_words_batch(self, words_batch: List[Dict], verse_text: str) -> Tuple[List[str], bool, str]:
        """
        ترجمه یک دسته از کلمات

        Args:
            words_batch: دسته کلمات برای ترجمه
            verse_text: متن کامل آیه

        Returns:
            (لیست ترجمه‌ها, موفقیت, پیام خطا)
        """
        if not words_batch:
            return [], True, ""

        # ساخت پرامپت
        prompt = self.prompts.build_translation_prompt(words_batch, verse_text)

        # ارسال درخواست
        response, success, error_msg = self.make_api_request(prompt)

        if success and response:
            # تجزیه پاسخ
            translations, parse_success = self.parse_translation_response(response, len(words_batch))

            if parse_success:
                self.stats["total_words_translated"] += len(words_batch)
                return translations, True, ""
            else:
                return [""] * len(words_batch), False, "خطا در تجزیه پاسخ"
        else:
            return [""] * len(words_batch), False, error_msg

#!/usr/bin/env python
"""
Simple test for /khatm/groups/ endpoint
"""
import requests
import json

def test_anonymous():
    """Test without token"""
    print("🔓 Testing Anonymous request...")
    
    headers = {
        'User-Agent': 'dart:io',
        'Accept': 'application/json'
    }
    
    try:
        response = requests.get('http://127.0.0.1:8000/khatm/groups/', headers=headers, timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success! Found {len(data)} groups")
                if data:
                    print(f"First group: {data[0].get('name', 'N/A')}")
            except:
                print(f"Response: {response.text[:200]}")
        else:
            print(f"❌ Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def test_with_token():
    """Test with a token"""
    print("\n🔐 Testing with token...")
    
    # You can replace this with a real token from your database
    token = "your_token_here"
    
    headers = {
        'User-Agent': 'dart:io',
        'Accept': 'application/json',
        'Authorization': f'Token {token}'
    }
    
    try:
        response = requests.get('http://127.0.0.1:8000/khatm/groups/', headers=headers, timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success! Found {len(data)} groups")
            except:
                print(f"Response: {response.text[:200]}")
        else:
            print(f"❌ Error: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_anonymous()
    test_with_token()

#!/usr/bin/env python3
"""
اسکریپت شمارش حدیث‌های ترجمه نشده
این اسکریپت تعداد حدیث‌هایی که در زبان‌های مختلف ترجمه نشده‌اند را نمایش می‌دهد
"""

import os
import sys
from collections import defaultdict
from django.db.models import Q
from django.core.wsgi import get_wsgi_application

# تنظیم متغیر محیطی برای تنظیمات Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')

import django
django.setup()
application = get_wsgi_application()

from apps.hadis.models import Hadis
from dj_language.models import Language

def get_available_languages():
    """دریافت لیست زبان‌های موجود در سیستم"""
    try:
        languages = Language.objects.filter(status=True).values_list('code', 'name')
        return list(languages)
    except:
        # اگر مدل Language در دسترس نبود، از لیست پیش‌فرض استفاده می‌کنیم
        return [
            ('en', 'English'),
            ('fa', 'Persian'),
            ('ar', 'Arabic'),
            ('ur', 'Urdu'),
            ('tr', 'Turkish'),
            ('fr', 'French'),
            ('de', 'German'),
            ('es', 'Spanish'),
            ('ru', 'Russian'),
            ('zh', 'Chinese'),
            ('hi', 'Hindi'),
            ('bn', 'Bengali'),
            ('pt', 'Portuguese'),
            ('id', 'Indonesian'),
            ('sw', 'Swahili'),
            ('az', 'Azerbaijani'),
            ('uz', 'Uzbek'),
            ('tg', 'Tajik'),
            ('gu', 'Gujarati'),
            ('ul', 'Roman Urdu')
        ]

def count_hadis_by_translation_status():
    """شمارش حدیث‌ها بر اساس وضعیت ترجمه"""
    
    print("=" * 80)
    print("📊 گزارش وضعیت ترجمه حدیث‌ها")
    print("=" * 80)
    
    # شمارش کل حدیث‌ها
    total_hadis = Hadis.objects.filter(status=True).count()
    print(f"📚 تعداد کل حدیث‌های فعال: {total_hadis:,}")
    
    # شمارش حدیث‌هایی که دارای ترجمه عربی یا فارسی هستند
    hadis_with_source = Hadis.objects.filter(
        Q(translations__contains=[{'language_code': 'ar'}]) |
        Q(translations__contains=[{'language_code': 'fa'}]) |
        Q(text__isnull=False, text__gt=''),
        status=True
    ).count()
    
    print(f"📖 حدیث‌های دارای متن منبع (عربی/فارسی): {hadis_with_source:,}")
    print(f"🚫 حدیث‌های فاقد متن منبع: {total_hadis - hadis_with_source:,}")
    print()
    
    # دریافت لیست زبان‌ها
    languages = get_available_languages()
    
    print("📋 وضعیت ترجمه به تفکیک زبان:")
    print("-" * 80)
    print(f"{'زبان':<20} {'کد':<8} {'ترجمه شده':<15} {'ترجمه نشده':<15} {'درصد':<10}")
    print("-" * 80)
    
    # آمار کلی
    total_translated = 0
    total_untranslated = 0
    language_stats = []
    
    for lang_code, lang_name in languages:
        # شمارش حدیث‌های ترجمه شده در این زبان
        translated_count = Hadis.objects.filter(
            translations__contains=[{'language_code': lang_code}],
            status=True
        ).count()
        
        # شمارش حدیث‌های ترجمه نشده در این زبان (که دارای متن منبع هستند)
        untranslated_hadis = Hadis.objects.filter(
            Q(translations__contains=[{'language_code': 'ar'}]) |
            Q(translations__contains=[{'language_code': 'fa'}]) |
            Q(text__isnull=False, text__gt=''),
            status=True
        ).exclude(
            translations__contains=[{'language_code': lang_code}]
        )
        
        untranslated_count = untranslated_hadis.count()
        
        # محاسبه درصد
        if hadis_with_source > 0:
            percentage = (translated_count / hadis_with_source) * 100
        else:
            percentage = 0
        
        language_stats.append({
            'code': lang_code,
            'name': lang_name,
            'translated': translated_count,
            'untranslated': untranslated_count,
            'percentage': percentage
        })
        
        print(f"{lang_name:<20} {lang_code:<8} {translated_count:<15,} {untranslated_count:<15,} {percentage:<10.1f}%")
        
        if lang_code not in ['ar', 'fa']:  # عربی و فارسی را در آمار کلی حساب نمی‌کنیم
            total_translated += translated_count
            total_untranslated += untranslated_count
    
    print("-" * 80)
    
    # نمایش آمار کلی
    print("\n📈 خلاصه آمار:")
    print(f"🟢 کل ترجمه‌های انجام شده: {total_translated:,}")
    print(f"🔴 کل ترجمه‌های باقی‌مانده: {total_untranslated:,}")
    
    if hadis_with_source > 0:
        avg_completion = (total_translated / (len(languages) * hadis_with_source)) * 100
        print(f"📊 میانگین پیشرفت کلی: {avg_completion:.1f}%")
    
    # نمایش زبان‌هایی که کمترین ترجمه را دارند
    print("\n🎯 اولویت‌های ترجمه (زبان‌هایی که کمترین ترجمه را دارند):")
    sorted_languages = sorted(language_stats, key=lambda x: x['translated'])
    
    for i, lang in enumerate(sorted_languages[:10], 1):
        if lang['code'] not in ['ar', 'fa']:
            print(f"{i:2d}. {lang['name']:<20} - {lang['untranslated']:,} حدیث باقی‌مانده")
    
    # نمایش زبان‌هایی که بیشترین ترجمه را دارند
    print("\n🏆 زبان‌هایی که بیشترین ترجمه را دارند:")
    sorted_languages_desc = sorted(language_stats, key=lambda x: x['translated'], reverse=True)
    
    for i, lang in enumerate(sorted_languages_desc[:5], 1):
        if lang['code'] not in ['ar', 'fa'] and lang['translated'] > 0:
            print(f"{i:2d}. {lang['name']:<20} - {lang['translated']:,} حدیث ({lang['percentage']:.1f}%)")
    
    print("\n" + "=" * 80)

def count_untranslated_for_specific_language(target_language_code):
    """شمارش حدیث‌های ترجمه نشده برای یک زبان خاص"""
    
    print(f"\n🔍 جزئیات ترجمه برای زبان: {target_language_code}")
    print("-" * 50)
    
    # حدیث‌هایی که دارای متن منبع هستند
    source_hadis = Hadis.objects.filter(
        Q(translations__contains=[{'language_code': 'ar'}]) |
        Q(translations__contains=[{'language_code': 'fa'}]) |
        Q(text__isnull=False, text__gt=''),
        status=True
    )
    
    # حدیث‌هایی که در زبان مقصد ترجمه شده‌اند
    translated_hadis = source_hadis.filter(
        translations__contains=[{'language_code': target_language_code}]
    )
    
    # حدیث‌هایی که در زبان مقصد ترجمه نشده‌اند
    untranslated_hadis = source_hadis.exclude(
        translations__contains=[{'language_code': target_language_code}]
    )
    
    print(f"📚 کل حدیث‌های قابل ترجمه: {source_hadis.count():,}")
    print(f"✅ ترجمه شده: {translated_hadis.count():,}")
    print(f"❌ ترجمه نشده: {untranslated_hadis.count():,}")
    
    if source_hadis.count() > 0:
        percentage = (translated_hadis.count() / source_hadis.count()) * 100
        print(f"📊 درصد تکمیل: {percentage:.1f}%")
    
    # نمایش چند نمونه از حدیث‌های ترجمه نشده
    if untranslated_hadis.exists():
        print(f"\n📝 نمونه‌ای از حدیث‌های ترجمه نشده:")
        for hadis in untranslated_hadis[:3]:
            print(f"  - شماره {hadis.number}: {hadis.text[:100]}...")
    
    return untranslated_hadis.count()

def main():
    """تابع اصلی"""
    
    # بررسی آرگومان‌های خط فرمان
    if len(sys.argv) > 1:
        target_language = sys.argv[1]
        count_untranslated_for_specific_language(target_language)
    else:
        count_hadis_by_translation_status()
        
        # پرسش برای نمایش جزئیات زبان خاص
        print("\n💡 برای مشاهده جزئیات یک زبان خاص، اسکریپت را با کد زبان اجرا کنید:")
        print("   مثال: python count_untranslated_hadis.py en")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  عملیات توسط کاربر متوقف شد.")
    except Exception as e:
        print(f"\n❌ خطا در اجرای اسکریپت: {e}")
        import traceback
        traceback.print_exc()
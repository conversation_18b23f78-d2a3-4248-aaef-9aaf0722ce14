#!/usr/bin/env python
"""
Test the view directly without HTTP requests
"""
import os
import django

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.develop')
django.setup()

from django.test import RequestFactory
from django.contrib.auth.models import AnonymousUser
from rest_framework.test import APIRequestFactory
from apps.khatm.views.group import GroupKhatmListCreateAPIView
from apps.account.models import User
from rest_framework.authtoken.models import Token
import random

def test_anonymous_user():
    """Test with anonymous user"""
    print("🔓 Testing Anonymous User...")

    factory = APIRequestFactory()
    request = factory.get('/khatm/groups/')
    request.user = AnonymousUser()
    request.LANGUAGE_CODE = 'en'
    
    view = GroupKhatmListCreateAPIView()
    view.request = request
    
    try:
        queryset = view.get_queryset()
        count = queryset.count()
        print(f"✅ Success! Anonymous user can access {count} public groups")
        
        if count > 0:
            first_group = queryset.first()
            print(f"📊 Sample group: {first_group.name} (Type: {first_group.group_type})")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_authenticated_user():
    """Test with authenticated user"""
    print("\n🔐 Testing Authenticated User...")
    
    # Get a random user
    users = User.objects.all()
    if not users.exists():
        print("⚠️  No users found in database")
        return False
    
    user = random.choice(users)
    print(f"📝 Testing with user: {user.username or user.email or f'User-{user.id}'}")
    
    factory = APIRequestFactory()
    request = factory.get('/khatm/groups/')
    request.user = user
    request.LANGUAGE_CODE = 'en'
    
    view = GroupKhatmListCreateAPIView()
    view.request = request
    
    try:
        queryset = view.get_queryset()
        count = queryset.count()
        print(f"✅ Success! Authenticated user can access {count} groups")
        
        if count > 0:
            first_group = queryset.first()
            print(f"📊 Sample group: {first_group.name} (Type: {first_group.group_type})")
        
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_with_page_filters():
    """Test page filters with anonymous user"""
    print("\n📄 Testing Page Filters with Anonymous User...")
    
    factory = APIRequestFactory()

    # Test 'created' filter
    request = factory.get('/khatm/groups/?page=created')
    request.user = AnonymousUser()
    request.LANGUAGE_CODE = 'en'
    
    view = GroupKhatmListCreateAPIView()
    view.request = request
    
    try:
        queryset = view.get_queryset()
        count = queryset.count()
        print(f"✅ 'created' filter: {count} groups (should ignore filter for anonymous)")
        return True
    except Exception as e:
        print(f"❌ Error with 'created' filter: {e}")
        return False

def main():
    print("🧪 Testing GroupKhatmListCreateAPIView directly")
    print("=" * 50)
    
    results = []
    
    # Test 1: Anonymous user
    results.append(test_anonymous_user())
    
    # Test 2: Authenticated user
    results.append(test_authenticated_user())
    
    # Test 3: Page filters
    results.append(test_with_page_filters())
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The fix is working correctly!")
    else:
        print(f"⚠️  {total - passed} test(s) failed.")

if __name__ == "__main__":
    main()

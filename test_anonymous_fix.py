#!/usr/bin/env python
"""
Test script to verify the anonymous user fix for /khatm/groups/ endpoint
"""
import requests
import json

# Test the endpoint without authentication (anonymous user)
def test_anonymous_access():
    url = "http://127.0.0.1:8000/khatm/groups/"
    
    print("🧪 Testing anonymous access to /khatm/groups/")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"✅ Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Response received successfully")
            print(f"📊 Number of groups returned: {len(data) if isinstance(data, list) else 'N/A'}")
            
            # Show first group if available
            if isinstance(data, list) and len(data) > 0:
                first_group = data[0]
                print(f"📝 First group: {first_group.get('name', 'N/A')} (Type: {first_group.get('group_type', 'N/A')})")
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection failed - Make sure Django server is running on port 8000")
    except Exception as e:
        print(f"❌ Error occurred: {e}")

if __name__ == "__main__":
    test_anonymous_access()
